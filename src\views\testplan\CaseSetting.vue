<template>
  <div class="case-setting-container" v-loading="loading">
    <!-- Top Toolbar -->
    <div class="toolbar top-toolbar">
      <div class="toolbar-left">
        <!-- 展开/收起全部按钮 -->
        <el-button @click="expandAll" type="primary" size="small" class="expand-button">
          <font-awesome-icon icon="up-right-and-down-left-from-center" /><span class="icon-button-text">Expand All</span>
        </el-button>
        <el-button @click="collapseAll" type="primary" size="small" class="collapse-button">
          <font-awesome-icon icon="down-left-and-up-right-to-center" /><span class="icon-button-text">Collapse All</span>
        </el-button>
      </div>
    </div>

    <!-- Content Area -->
    <div class="content-area">
      <el-collapse v-model="activeNames">
        <div class="card-container">
          <el-collapse-item title="Case Setting" name="case" class="custom-card">
            <el-form :model="config" label-width="160px" label-position="top">
              <!-- 第一行：请求ID、Is Ext Flag、响应ID -->
              <div class="form-row">
                <el-form-item label="Request ID" class="form-item">
                  <el-input v-model="requestIdInput" placeholder="Enter ID in hex, e.g. 0x731"
                    @change="handleRequestIdChange" />
                </el-form-item>

                <el-form-item label="Ext Flag" class="form-item" style="width: 80px;">
                  <el-switch v-model="config.requestIsExt" />
                </el-form-item>
              </div>
              <div class="form-row">
                <el-form-item label="Response ID" class="form-item">
                  <el-input v-model="responseIdInput" placeholder="Enter ID in hex, e.g. 0x631"
                    @change="handleResponseIdChange" />
                </el-form-item>

                <el-form-item label="Timeout (ms)" class="form-item">
                  <el-input-number v-model="config.timeoutMs" :min="1" :max="60000" style="width: 200px" />
                </el-form-item>
              </div>

              <div class="comment-row">
                <el-icon>
                  <InfoFilled />
                </el-icon> <span>ISO-11898 and ISO-14229 protocols share the same Response ID, and Timeout
                  setting.</span>
              </div>
            </el-form>
          </el-collapse-item>
        </div>

        <div class="card-container">
          <el-collapse-item title="Database Setting" name="database" class="custom-card">
            <el-form :model="config" label-width="160px" label-position="top">
              <!-- Import DBC 按钮 -->
              <el-form-item>
                <el-button @click="handleImportDbc" :loading="importLoading" type="primary" size="small"
                  style="margin-bottom: 15px;">
                  Import DBC
                </el-button>
              </el-form-item>

              <!-- 节点选择 -->
              <el-form-item label="Target ECU (DUT)" v-if="nodeNames.length > 0">
                <el-select v-model="selectedNode" placeholder="Select target ECU" style="width: 100%;"
                  @change="handleNodeChange">
                  <el-option v-for="node in nodeNames" :key="node" :label="node" :value="node" />
                </el-select>
              </el-form-item>

              <el-form-item :label="`White List Frames (${filteredFrames.length})`">
                <div class="import-note" v-if="allFrames.length === 0">
                  No frames imported. Please use "Import DBC" button above to add frames.
                </div>
                <div v-else-if="!selectedNode" class="node-selection-required">
                  Please select a target ECU to view related frames
                </div>
                <el-table :data="filteredFrames" style="width: 100%" v-else :max-height="400" border>
                  <el-table-column label="ID" min-width="120">
                    <template #default="{ row }">
                      0x{{ row.id.toString(16).toUpperCase().padStart(3, '0') }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="name" label="Name" min-width="180" />
                  <el-table-column label="DLC" min-width="80">
                    <template #default="{ row }">
                      0x{{ row.dlc.toString(16).toUpperCase() }}
                    </template>
                  </el-table-column>
                  <el-table-column label="Ext Flag" min-width="120">
                    <template #default="{ row }">
                      {{ row.isExt ? 'CAN Extended' : 'CAN Standard' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="transmitter" label="Transmitter" min-width="120" />
                  <el-table-column label="Receivers" min-width="180">
                    <template #default="{ row }">
                      {{ row.receivers.join(', ') }}
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-form>
          </el-collapse-item>
        </div>

        <div class="card-container">
          <el-collapse-item title="NM Wake Up Setting" name="nmWakeup" class="custom-card">
            <el-form :model="config" label-width="160px" label-position="top">
              <!-- 第一行：Enable NM Wake Up -->
              <el-form-item label="Enable NM Wake Up">
                <el-switch v-model="config.enableNmWakeup" />
              </el-form-item>

              <div :class="{ 'disabled-form-content': !config.enableNmWakeup }">
                <!-- 表格布局：ID、IsExtFlag、Frame Type、DLC、Data -->
                <el-table :data="[{}]" border style="width: 100%; margin-bottom: 15px;" :show-header="true"
                  class="nm-table">
                  <el-table-column label="ID" width="220">
                    <template #default>
                      <el-input v-model="nmWakeupIdInput" placeholder="Enter ID in hex" @change="handleNmWakeupIdChange"
                        :disabled="!config.enableNmWakeup" />
                    </template>
                  </el-table-column>
                  <el-table-column label="Ext Flag" width="100">
                    <template #default>
                      <el-switch v-model="config.nmWakeupIsExt" :disabled="!config.enableNmWakeup" />
                    </template>
                  </el-table-column>
                  <el-table-column label="Frame Type" width="200">
                    <template #default>
                      <el-select v-model="nmWakeupFrameType" :disabled="!config.enableNmWakeup">
                        <el-option value="CAN Frame" label="CAN Frame" />
                        <el-option value="CANFD Frame" label="CANFD Frame" />
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="DLC" width="150">
                    <template #default>
                      <el-input v-model="nmWakeupDlcInput" placeholder="Enter DLC" @change="handleNmWakeupDlcChange"
                        :disabled="!config.enableNmWakeup" />
                    </template>
                  </el-table-column>
                  <el-table-column label="Data">
                    <template #default>
                      <el-input v-model="nmWakeupDataInput"
                        placeholder="Enter data bytes, e.g. 0x3F,0x50,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF"
                        @change="handleNmWakeupDataChange" :disabled="!config.enableNmWakeup" />
                    </template>
                  </el-table-column>
                </el-table>

                <!-- 第三行：Cycle、Wake Up Delay -->
                <div class="nm-wakeup-row">
                  <!-- Cycle -->
                  <el-form-item label="Cycle (ms)" class="nm-wakeup-item">
                    <el-input-number v-model="config.nmWakeupCycleMs" :min="1" :max="60000" style="width: 200px"
                      :disabled="!config.enableNmWakeup" />
                  </el-form-item>

                  <!-- Wake Up Delay -->
                  <el-form-item label="Wake Up Delay (ms)" class="nm-wakeup-item">
                    <el-input-number v-model="config.nmWakeupDelayMs" :min="0" :max="60000" style="width: 200px"
                      :disabled="!config.enableNmWakeup" />
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </el-collapse-item>
        </div>

        <div class="card-container">
          <el-collapse-item title="UDS Setting" name="uds" class="custom-card">
            <el-form :model="config" label-width="160px" label-position="top">
              <!-- 第一行：MTU 和重试设置 -->
              <div class="form-row">
                <el-form-item label="DUT MTU < 4096 bytes" class="form-item">
                  <el-switch v-model="config.isDutMtuLessThan4096" />
                </el-form-item>

                <el-form-item label="Enable Retry Request" class="form-item">
                  <el-switch v-model="config.enableDiagRetryRequest" />
                </el-form-item>

                <el-form-item label="Retry Request Payload" class="form-item" style="width: 400px;">
                  <el-input v-model="diagRetryRequestPayloadInput" placeholder="Enter data bytes, e.g. 0x10,0x01"
                    @change="handleDiagRetryRequestPayloadChange" :disabled="!config.enableDiagRetryRequest" />
                </el-form-item>
              </div>

              <div class="comment-row">
                <el-icon>
                  <InfoFilled />
                </el-icon> <span>Retry Request will be triggered when UDS diagnostic requests receive no response, using the configured payload for retry attempts.</span>
              </div>

              <div>
                <el-form-item label="Security Access Dll">
                  <div class="security-input-group">
                    <el-input v-model="securityDllDisplay" placeholder="No Dll selected" readonly
                      class="security-dll-input"></el-input>
                    <div class="security-buttons">
                      <el-button @click="selectDll" type="primary" :icon="Plus"
                        :title="config.securityInfo?.hasDll ? 'Replace DLL' : 'Select DLL'" />
                      <el-button v-if="config.securityInfo?.hasDll" @click="removeDll" type="danger" :icon="Delete"
                        title="Remove DLL" />
                    </div>
                  </div>
                  <div v-if="selectedDllName" class="selected-dll-info">
                    {{ config.securityInfo?.hasDll ? 'DLL will be replaced. Click Save to apply.' : 'DLL selected. Click Save to apply.' }}
                  </div>
                  <div v-if="shouldRemoveDll" class="selected-dll-info">
                    DLL marked for removal. Click Save to apply.
                  </div>
                </el-form-item>
              </div>
            </el-form>
          </el-collapse-item>
        </div>

        <div class="card-container">
          <el-collapse-item title="Log Setting" name="log" class="custom-card">
            <el-form :model="config" label-width="160px" label-position="top">
              <div class="form-row">
                <el-form-item label="Enable log filter" class="form-item">
                  <el-switch v-model="config.enableLogFilter" />
                </el-form-item>
              </div>

              <div class="comment-row">
                <el-icon>
                  <InfoFilled />
                </el-icon> <span>When enabled, BLF log files will only record transmitted frames, expected received frames, and error frames to reduce log size and improve performance.</span>
              </div>
            </el-form>
          </el-collapse-item>
        </div>
      </el-collapse>
    </div>

    <!-- Bottom Toolbar -->
    <div class="toolbar bottom-toolbar">
      <el-button type="primary" @click="handleSave">Save</el-button>
      <el-button @click="resetToDefault" style="margin-left: 10px;">Reset</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Delete, InfoFilled, Plus, } from '@element-plus/icons-vue';
import { caseApi, CaseConfigDto, WhiteListFrame } from '@/api/caseApi';

const loading = ref(false);
const config = ref<CaseConfigDto>({
  whiteListFrames: [],
  enableNmWakeup: true,
  nmWakeupId: 0x53F,
  nmWakeupIsExt: false,
  nmWakeupDlc: 8,
  nmWakeupData: [0x3F, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF],
  nmWakeupCommunicationType: 'Can',
  nmWakeupCycleMs: 100,
  nmWakeupDelayMs: 2000,
  requestId: 0x731,
  requestIsExt: false,
  responseId: 0x631,
  timeoutMs: 500,
  isDutMtuLessThan4096: false,
  enableDiagRetryRequest: false,
  diagRetryRequestPayload: [0x10, 0x01],
  securityInfo: {
    hasDll: false,
    dllFileName: undefined,
    dllSize: 0
  }
});

// 新增变量
const nodeNames = ref<string[]>([]);
const selectedNode = ref<string>('');
const allFrames = ref<WhiteListFrame[]>([]);

// 根据选定节点过滤帧
const filteredFrames = computed(() => {
  if (!selectedNode.value) return [];

  const txFrames = allFrames.value.filter(frame =>
    frame.transmitter === selectedNode.value
  );

  const rxFrames = allFrames.value.filter(frame =>
    frame.transmitter !== selectedNode.value &&
    frame.receivers.includes(selectedNode.value)
  );

  return [...txFrames, ...rxFrames];
});

const activeNames = ref(['case', 'database', 'nmWakeup', 'uds', 'log']); // 默认展开所有分组

const nmWakeupIdInput = ref('');
const nmWakeupDlcInput = ref('');
const nmWakeupDataInput = ref('');
const requestIdInput = ref('');
const responseIdInput = ref('');
const diagRetryRequestPayloadInput = ref('');

// 用于显示和选择 Frame Type
const nmWakeupFrameType = computed({
  get: () => {
    return config.value.nmWakeupCommunicationType === 'Can' ? 'CAN Frame' : 'CANFD Frame';
  },
  set: (value: string) => {
    config.value.nmWakeupCommunicationType = value === 'CAN Frame' ? 'Can' : 'Canfd';
  }
});

const selectedDllPath = ref<string | null>(null);
const selectedDllName = ref<string | null>(null);
const shouldRemoveDll = ref(false);

const defaultConfig: CaseConfigDto = {
  whiteListFrames: [], // 修改为空数组
  enableNmWakeup: true,
  nmWakeupId: 0x53F,
  nmWakeupIsExt: false,
  nmWakeupDlc: 8,
  nmWakeupData: [0x3F, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF],
  nmWakeupCommunicationType: 'Can',
  nmWakeupCycleMs: 100,
  nmWakeupDelayMs: 2000,
  requestId: 0x731,
  requestIsExt: false,
  responseId: 0x631,
  timeoutMs: 500,
  isDutMtuLessThan4096: false,
  enableDiagRetryRequest: false,
  diagRetryRequestPayload: [0x10, 0x01],
  securityInfo: {
    hasDll: false,
    dllFileName: undefined,
    dllSize: 0
  }
};

const arrayToHexString = (arr?: number[]) => {
  if (!arr) {
    return '';
  }
  return arr.map(n => '0x' + n.toString(16).toUpperCase()).join(',');
};

const handleNmWakeupIdChange = (value: string) => {
  try {
    // 移除可能的 0x 前缀
    const cleanValue = value.replace(/^0x/i, '');
    const id = parseInt(cleanValue, 16);
    if (isNaN(id)) {
      ElMessage.error('Invalid ID format');
      return;
    }
    config.value.nmWakeupId = id;
    // 确保显示带有 0x 前缀
    nmWakeupIdInput.value = '0x' + id.toString(16).toUpperCase();
  } catch (error) {
    ElMessage.error('Invalid input value');
  }
};

const handleNmWakeupDlcChange = (value: string) => {
  try {
    // 移除可能的 0x 前缀
    const cleanValue = value.replace(/^0x/i, '');
    const dlc = parseInt(cleanValue, 16);
    if (isNaN(dlc)) {
      ElMessage.error('Invalid DLC format');
      return;
    }
    if (dlc < 0 || dlc > 64) {
      ElMessage.warning('DLC should be between 0 and 64');
      return;
    }
    config.value.nmWakeupDlc = dlc;
    // 确保显示带有 0x 前缀
    nmWakeupDlcInput.value = '0x' + dlc.toString(16).toUpperCase();
  } catch (error) {
    ElMessage.error('Invalid input value');
  }
};

const handleNmWakeupDataChange = (value: string) => {
  try {
    const bytes = value.split(',')
      .map(s => s.trim())
      .filter(s => s)
      .map(s => parseInt(s, 16));

    if (bytes.length > 8) {
      ElMessage.warning('Maximum 8 bytes allowed');
      return;
    }
    if (bytes.some(b => isNaN(b) || b < 0 || b > 0xFF)) {
      ElMessage.warning('Each byte must be between 0x00 and 0xFF');
      return;
    }
    config.value.nmWakeupData = bytes;
  } catch (error) {
    ElMessage.error('Invalid input format');
  }
};

const handleRequestIdChange = (value: string) => {
  try {
    // 移除可能的 0x 前缀
    const cleanValue = value.replace(/^0x/i, '');
    const id = parseInt(cleanValue, 16);
    if (isNaN(id)) {
      ElMessage.error('Invalid ID format');
      return;
    }
    config.value.requestId = id;
    // 确保显示带有 0x 前缀
    requestIdInput.value = '0x' + id.toString(16).toUpperCase();
  } catch (error) {
    ElMessage.error('Invalid input value');
  }
};

const handleResponseIdChange = (value: string) => {
  try {
    // 移除可能的 0x 前缀
    const cleanValue = value.replace(/^0x/i, '');
    const id = parseInt(cleanValue, 16);
    if (isNaN(id)) {
      ElMessage.error('Invalid ID format');
      return;
    }
    config.value.responseId = id;
    // 确保显示带有 0x 前缀
    responseIdInput.value = '0x' + id.toString(16).toUpperCase();
  } catch (error) {
    ElMessage.error('Invalid input value');
  }
};

const handleDiagRetryRequestPayloadChange = (value: string) => {
  try {
    const bytes = value.split(',')
      .map(s => s.trim())
      .filter(s => s)
      .map(s => parseInt(s, 16));

    if (bytes.some(b => isNaN(b) || b < 0 || b > 0xFF)) {
      ElMessage.warning('Each byte must be between 0x00 and 0xFF');
      return;
    }
    config.value.diagRetryRequestPayload = bytes;
  } catch (error) {
    ElMessage.error('Invalid input format');
  }
};

const resetToDefault = () => {
  config.value = JSON.parse(JSON.stringify(defaultConfig));
  nmWakeupIdInput.value = '0x' + defaultConfig.nmWakeupId.toString(16).toUpperCase();
  nmWakeupDlcInput.value = '0x' + defaultConfig.nmWakeupDlc.toString(16).toUpperCase();
  nmWakeupDataInput.value = arrayToHexString(defaultConfig.nmWakeupData);
  requestIdInput.value = '0x' + defaultConfig.requestId.toString(16).toUpperCase();
  responseIdInput.value = '0x' + defaultConfig.responseId.toString(16).toUpperCase();
  diagRetryRequestPayloadInput.value = arrayToHexString(defaultConfig.diagRetryRequestPayload);
  selectedDllPath.value = null;
  selectedDllName.value = null;
  shouldRemoveDll.value = false;

  // 重置节点和帧相关状态
  nodeNames.value = [];
  selectedNode.value = '';
  allFrames.value = [];
};

const loadConfig = async () => {
  loading.value = true;
  try {
    const response = await caseApi.getCaseConfig();
    config.value = response.data;
    nmWakeupIdInput.value = '0x' + response.data.nmWakeupId.toString(16).toUpperCase();
    nmWakeupDataInput.value = arrayToHexString(response.data.nmWakeupData);
    requestIdInput.value = '0x' + response.data.requestId.toString(16).toUpperCase();
    responseIdInput.value = '0x' + response.data.responseId.toString(16).toUpperCase();

    // 设置备用诊断请求数据
    if (response.data.diagRetryRequestPayload) {
      diagRetryRequestPayloadInput.value = arrayToHexString(response.data.diagRetryRequestPayload);
    } else {
      diagRetryRequestPayloadInput.value = '0x10,0x01';
    }

    // 确保新增字段有默认值
    if (response.data.nmWakeupIsExt === undefined) {
      config.value.nmWakeupIsExt = false;
    }
    if (response.data.nmWakeupDlc === undefined) {
      config.value.nmWakeupDlc = 8;
    }
    if (response.data.requestIsExt === undefined) {
      config.value.requestIsExt = false;
    }
    if (response.data.enableDiagRetryRequest === undefined) {
      config.value.enableDiagRetryRequest = false;
    }
    if (response.data.diagRetryRequestPayload === undefined) {
      config.value.diagRetryRequestPayload = [0x10, 0x01];
    }

    // 设置 DLC 输入框的值
    nmWakeupDlcInput.value = '0x' + config.value.nmWakeupDlc.toString(16).toUpperCase();

    // 如果已存在帧，尝试提取节点列表
    if (response.data.whiteListFrames.length > 0) {
      const frames = response.data.whiteListFrames;
      allFrames.value = frames;

      // 提取所有唯一的节点名
      const nodes = new Set<string>();
      frames.forEach(frame => {
        if (frame.transmitter) nodes.add(frame.transmitter);
        frame.receivers?.forEach(r => nodes.add(r));
      });

      nodeNames.value = Array.from(nodes);

      // 如果保存了选中的节点，恢复选中状态
      if (response.data.selectedNodeName && nodeNames.value.includes(response.data.selectedNodeName)) {
        selectedNode.value = response.data.selectedNodeName;
      }
      // 否则，如果只有一个节点，自动选择
      else if (nodeNames.value.length === 1) {
        selectedNode.value = nodeNames.value[0];
      }
    }
  } catch (error) {
    ElMessage.error('Failed to load configuration');
  } finally {
    loading.value = false;
  }
};

// 处理节点改变
const handleNodeChange = (value: string) => {
  // 更新过滤后的帧列表 (通过 computed 自动实现)
  if (!value) {
    ElMessage.warning('Please select a target ECU');
  }
};

const handleSave = async () => {
  loading.value = true;
  try {
    // 准备提交数据
    const submitData = { ...config.value };

    // 修改: 保存所有帧数据，而不只是筛选后的数据
    submitData.whiteListFrames = allFrames.value;

    // 保存选中的节点名称
    submitData.selectedNodeName = selectedNode.value;

    if (selectedDllPath.value) {
      submitData.securityDllPath = selectedDllPath.value;
    }

    if (shouldRemoveDll.value) {
      submitData.removeSecurityDll = true;
    }

    const result = await caseApi.updateCaseConfig(submitData);
    config.value = result.data;

    selectedDllPath.value = null;
    selectedDllName.value = null;
    shouldRemoveDll.value = false;

    ElMessage.success('Case settings saved.');
  } catch (error) {
    ElMessage.error('Save failed');
  } finally {
    loading.value = false;
  }
};

const importLoading = ref(false);

const handleImportDbc = async () => {
  importLoading.value = true;
  try {
    const response = await caseApi.importDbc();

    // 保存所有帧和节点信息
    allFrames.value = response.data.whiteListFrames;
    nodeNames.value = response.data.nodeNames;

    // 重置选择的节点
    selectedNode.value = '';

    // 清空当前已保存的帧列表，等待用户选择节点
    config.value.whiteListFrames = [];

    ElMessage.success('DBC file imported successfully. Please select a target ECU.');
  } catch (error: any) {
    if (error.response?.data === 'UserCanceled') {
      return;
    }
    ElMessage.error(error.response?.data === 'InvalidFileFormat'
      ? 'Invalid DBC file format'
      : 'Failed to import DBC file');
  } finally {
    importLoading.value = false;
  }
};

const formatFileSize = (bytes?: number): string => {
  if (!bytes) return '0 B';

  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(2)} ${units[unitIndex]}`;
};

const selectDll = async () => {
  try {
    const response = await caseApi.selectSecurityDll();
    const dllPath = response.data.path;
    selectedDllPath.value = dllPath;
    selectedDllName.value = dllPath.split('\\').pop() || dllPath;
    // 清除删除标记，因为用户选择了新的DLL
    shouldRemoveDll.value = false;
  } catch (error: any) {
    if (error.response?.data === 'UserCanceled') {
      return;
    }
    ElMessage.error(error.response?.data === 'InvalidFileFormat'
      ? 'Invalid DLL file format'
      : 'Failed to select DLL file');
  }
};

const removeDll = () => {
  shouldRemoveDll.value = true;
};

const securityDllDisplay = computed(() => {
  if (selectedDllName.value) {
    return selectedDllName.value;
  }

  if (shouldRemoveDll.value) {
    return 'DLL will be removed after save';
  }

  return config.value.securityInfo?.hasDll
    ? `${config.value.securityInfo.dllFileName} (${formatFileSize(config.value.securityInfo.dllSize)})`
    : '';
});



// 展开全部面板
const expandAll = () => {
  activeNames.value = ['case', 'database', 'nmWakeup', 'uds', 'log'];
};

// 收起全部面板
const collapseAll = () => {
  activeNames.value = [];
};

onMounted(() => {
  loadConfig();
});
</script>

<style scoped>
.case-setting-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 15px 20px;
}

.toolbar {
  display: flex;
  background-color: #ffffff;
  z-index: 10;
}

.top-toolbar {
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 10px;
  position: sticky;
  top: 0;
}

.bottom-toolbar {
  border-top: 1px solid #e4e7ed;
  padding-top: 10px;
  position: sticky;
  bottom: 0;
}

.content-area {
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
}

/* 卡片容器样式 */
.card-container {
  margin-bottom: 15px;
  margin-right: 10px;
}

/* 自定义卡片样式 */
.custom-card {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  background-color: #fff;
}

:deep(.el-collapse-item__header) {
  font-size: 16px;
  font-weight: bold;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  transition: background-color 0.3s;
}

:deep(.el-collapse) {
  border: none;
}

:deep(.el-collapse-item__wrap) {
  padding: 15px;
  border: none;
  background-color: #ffffff;
}

.disabled-form-content {
  opacity: 0.6;
  pointer-events: none;
}

.disabled-form-content :deep(.el-input__wrapper),
.disabled-form-content :deep(.el-input-number__wrapper),
.disabled-form-content :deep(.el-select) {
  cursor: not-allowed;
}

.security-config {
  padding: 10px;
}

.dll-info {
  margin-bottom: 15px;
}

.security-input-group {
  display: flex;
  align-items: center;
}

.security-dll-input {
  width: 400px;
}

.security-buttons {
  margin-left: 10px;
  display: flex;
  gap: 5px;
}

.selected-dll-info {
  margin-left: 10px;
  margin-top: 5px;
  font-size: 12px;
  color: var(--el-color-warning);
}

.warning-text {
  color: #E6A23C;
}

.node-selection-required {
  color: #E6A23C;
  text-align: center;
  padding: 20px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  margin-bottom: 10px;
}

.import-note {
  color: #909399;
  font-style: italic;
  text-align: center;
}

.white-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.toolbar-left {
  display: flex;
}

.expand-button,
.collapse-button {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* NM 唤醒表格样式 */
.nm-table {
  margin-bottom: 15px;
}

:deep(.nm-table .el-table__header) {
  font-weight: bold;
}

:deep(.nm-table .el-table__cell) {
  padding: 8px;
}

:deep(.nm-table .el-input__wrapper),
:deep(.nm-table .el-select) {
  width: 100%;
}

/* NM Wake Up 设置的行布局 */
.nm-wakeup-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 10px;
  justify-content: flex-start;
  /* 左对齐 */
}

.nm-wakeup-item {
  flex: 0 0 auto;
  /* 不自动拉伸，保持原始大小 */
}

/* ID、Is Ext Flag、Frame Type、DLC 的宽度 */
.nm-wakeup-item:nth-child(1) {
  width: 180px;
}

.nm-wakeup-item:nth-child(2) {
  width: 80px;
}

.nm-wakeup-item:nth-child(3) {
  width: 200px;
}

.nm-wakeup-item:nth-child(4) {
  width: 150px;
}

/* Data 字段随页面大小变化 */
.nm-wakeup-item:nth-child(5) {
  flex: 1 1 auto;
  /* 可以拉伸和收缩 */
  min-width: 300px;
}

/* 第三行的元素左对齐 */
.nm-wakeup-row:nth-child(2) .nm-wakeup-item {
  width: 180px;
}

/* UDS Setting 设置的行布局 */
.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 10px;
  justify-content: flex-start;
}

.form-item {
  flex: 0 0 auto;
  width: 200px;
}

.comment-row {
  color: #888;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 6px;
  margin-bottom: 10px;
}

.el-form-item--default {
  margin-bottom: 4px;
}
</style>
