<template>
  <div class="test-run-container">
    <!-- 顶部控制区 -->
    <div class="toolbar">
      <h3>Test Execution</h3>
      <div class="action-buttons">
        <!-- 开始按钮：在测试未运行且未暂停时显示 -->
        <el-button
          v-if="!isRunning && !isPaused"
          type="success"
          size="small"
          :icon="CaretRight"
          :loading="starting"
          @click="startTestExecution"
          :disabled="savedTestCasesCount === 0">
          Start
        </el-button>

        <!-- 暂停按钮：只在测试运行且未暂停时显示 -->
        <el-button
          v-if="isRunning && !isPaused"
          type="warning"
          size="small"
          :icon="VideoPause"
          :loading="pausing"
          @click="pauseTestExecution">
          Pause
        </el-button>

        <!-- 恢复按钮：只在测试已暂停时显示 -->
        <el-button
          v-if="isPaused"
          type="info"
          size="small"
          :icon="CaretRight"
          :loading="resuming"
          @click="resumeTestExecution">
          Resume
        </el-button>

        <!-- 停止按钮：在测试运行或暂停时都显示 -->
        <el-button
          v-if="isRunning || isPaused"
          type="danger"
          size="small"
          :icon="VideoPlay"
          :loading="stopping"
          @click="stopTestExecution">
          Stop
        </el-button>
      </div>
    </div>

    <!-- 测试进度显示区域 - 始终显示 -->
    <div class="content-area">
      <!-- 状态概览和统计区域 - 左右布局 -->
      <div class="overview-layout">
        <!-- 左侧：进度圆环 -->
        <div class="progress-section panel">
          <div class="progress-circle-container">
            <el-progress
              type="dashboard"
              :percentage="progressPercentage"
              :width="180"
              :stroke-width="12"
              :format="format">
            </el-progress>
            <div class="test-state">
              <TestStateTag :state="runStatus.processState" />
            </div>
          </div>
        </div>
        
        <!-- 右侧：统计卡片 -->
        <div class="stats-section">
          <div class="stats-grid">
            <!-- 总用例卡片 - 移到第一个位置 -->
            <div class="stat-card total panel">
              <div class="stat-icon">
                <el-icon><InfoFilled /></el-icon>
              </div>
              <div class="stat-data">
                <div class="stat-value">{{ totalCount }}</div>
                <div class="stat-label">Total Test Cases</div>
              </div>
            </div>
            
            <!-- 完成率卡片 - 移到第二个位置 -->
            <div class="stat-card completion panel">
              <div class="stat-icon">
                <el-icon><Histogram /></el-icon>
              </div>
              <div class="stat-data">
                <div class="stat-value">{{ completionRate }}%</div>
                <div class="stat-label">Completion Rate</div>
              </div>
            </div>
            
            <!-- 成功用例卡片 - 移到第三个位置 -->
            <div class="stat-card success panel">
              <div class="stat-icon">
                <el-icon><CircleCheckFilled /></el-icon>
              </div>
              <div class="stat-data">
                <div class="stat-value">{{ runStatus.testResult?.successCount || 0 }}</div>
                <div class="stat-label">Passed Test Cases</div>
              </div>
            </div>
            
            <!-- 失败用例卡片 - 移到第四个位置 -->
            <div class="stat-card failure panel">
              <div class="stat-icon">
                <el-icon><CircleCloseFilled /></el-icon>
              </div>
              <div class="stat-data">
                <div class="stat-value">{{ runStatus.testResult?.failureCount || 0 }}</div>
                <div class="stat-label">Failed Test Cases</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 当前操作信息 - 如果有内容则显示 -->
      <div v-if="runStatus.currentOperation" class="operation-panel panel">
        <div class="current-operation">
          <span class="operation-label">Status:</span>
          <span class="operation-value">{{ runStatus.currentOperation }}</span>
        </div>
      </div>

      <!-- 底部信息区域 -->
      <div class="footer-panel panel">
        <div class="timing-info">
          <div v-if="startTime" class="time-item">
            <span class="time-label">Start:</span>
            <span class="time-value">{{ startTime }}</span>
          </div>
          <div v-if="startTime && (isRunning || isPaused)" class="time-item">
            <span class="time-label">Elapsed:</span>
            <span class="time-value">{{ formatDurationWithoutMs(elapsedTimeMs) }}</span>
          </div>
        </div>
        <div class="last-updated" v-if="lastUpdated">
          Last updated: {{ lastUpdated }}
        </div>
      </div>      
    </div>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable */
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { appApi, TesterSnapshot, ExecutionState, isTesterCompleted } from '@/api/appApi';
import { 
  CircleCheckFilled, 
  CircleCloseFilled, 
  InfoFilled, 
  VideoPlay,
  VideoPause,
  CaretRight,
  Histogram,
  Monitor
} from '@element-plus/icons-vue';
import TestStateTag from '@/components/common/TestStateTag.vue';
import { formatDuration, formatDateTime } from '@/utils/timeUtils';

// 状态变量
const savedTestCasesCount = ref(0);
const loading = ref(true);
const starting = ref(false);
const stopping = ref(false);
const pausing = ref(false);
const resuming = ref(false);
const startTime = ref('');
// 将 estimatedEndTime 从 ref 变为 computed
const runStatus = ref<TesterSnapshot>({
  processState: ExecutionState.Pending,
  currentOperation: '',
  testResult: {
    id: '',
    resultFolderName: '',
    testType: '',
    creationTime: '',
    totalCount: 0,
    successCount: 0,
    failureCount: 0
  },
  caseResults: []
});
const lastUpdated = ref('');

// 状态轮询定时器
let statusPollingTimer: number | null = null;

// 计算属性
const isRunning = computed(() => {
  return runStatus.value.processState === ExecutionState.Running;
});

const isPaused = computed(() => {
  return runStatus.value.processState === ExecutionState.Paused;
});

const totalCount = computed(() => {
  // 如果testResult为null，使用savedTestCasesCount
  if (!runStatus.value.testResult || runStatus.value.testResult.totalCount === 0) {
    return savedTestCasesCount.value;
  }
  return runStatus.value.testResult.totalCount;
});

const completedCount = computed(() => {
  // 如果testResult为null，返回0
  if (!runStatus.value.testResult) {
    return 0;
  }
  return (runStatus.value.testResult.successCount || 0) + (runStatus.value.testResult.failureCount || 0);
});

const progressPercentage = computed(() => {
  if (totalCount.value === 0) return 0;
  return Math.round((completedCount.value / totalCount.value) * 100);
});

const completionRate = computed(() => {
  if (totalCount.value === 0) return 0;
  // 计算完成率，保留两位小数
  return (completedCount.value / totalCount.value * 100).toFixed(2);
});

// 格式化进度显示
const format = (percentage: number) => {
  const text = `${completedCount.value} / ${totalCount.value}`;
  
  // 使用 nextTick 确保 DOM 已更新后再设置class
  nextTick(() => {
    const progressText = document.querySelector('.el-progress__text');
    if (progressText) {
      // 清除之前的字体大小class
      progressText.classList.remove('font-small', 'font-medium', 'font-large');
      
      // 根据文字长度添加对应的class
      if (text.length <= 11) {
        progressText.classList.add('font-large');
      } else if (text.length <= 14) {
        progressText.classList.add('font-medium');
      } else {
        progressText.classList.add('font-small');
      }
    }
  });
  
  return text;
};

// 计算已执行时长(毫秒)
const elapsedTimeMs = computed(() => {
  if (!startTime.value) {
    return 0;
  }

  // 通过访问 estimatedEndTime 来触发这个计算属性的更新
  // 当 estimatedEndTime 变化时，这个计算属性也会重新计算
  estimatedEndTime.value; // 触发依赖

  const startTimeObj = new Date(startTime.value);
  return Date.now() - startTimeObj.getTime();
});

// 格式化时长但不显示毫秒
const formatDurationWithoutMs = (ms: number): string => {
  ms = Math.floor(ms);

  if (ms < 1000) {
    return '1s';
  } else if (ms < 60000) {
    const seconds = Math.floor(ms / 1000);
    return `${seconds}s`;
  } else if (ms < 3600000) {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  } else {
    const hours = Math.floor(ms / 3600000);
    const minutes = Math.floor((ms % 3600000) / 60000);
    return `${hours}h ${minutes}m`;
  }
};

// 计算剩余时间(毫秒)
const remainingTimeMs = computed(() => {
  if (!startTime.value || totalCount.value === 0 || completedCount.value === 0 || !isRunning.value) {
    return 0;
  }

  const startTimeObj = new Date(startTime.value);
  const elapsedMs = Date.now() - startTimeObj.getTime();
  const msPerCase = elapsedMs / completedCount.value;
  const remainingCases = totalCount.value - completedCount.value;
  return msPerCase * remainingCases;
});

// 计算估计完成时间 - 改为计算属性
const estimatedEndTime = computed(() => {
  if (!startTime.value || totalCount.value === 0 || completedCount.value === 0 || !isRunning.value) {
    return '';
  }

  const startTimeObj = new Date(startTime.value);
  const elapsedMs = Date.now() - startTimeObj.getTime();
  const msPerCase = elapsedMs / completedCount.value;
  const remainingCases = totalCount.value - completedCount.value;
  const remainingMs = msPerCase * remainingCases;
  
  if (remainingMs <= 0) {
    return 'Completing...';
  }
  
  const endTimeObj = new Date(Date.now() + remainingMs);
  return formatDateTime(endTimeObj);
});

// 获取保存的测试用例数量
const fetchSavedTestCasesCount = async () => {
  loading.value = true;
  try {
    const response = await appApi.getCaseCollectionInfo();
    savedTestCasesCount.value = response.data.caseCount;
  } catch (error) {
    console.error('获取保存的测试用例数量失败:', error);
    ElMessage.error('Failed to fetch test cases count');
  } finally {
    loading.value = false;
  }
};

// 开始测试执行
const startTestExecution = async () => {
  if (savedTestCasesCount.value === 0) {
    ElMessage.warning('No test cases available to execute');
    return;
  }

  starting.value = true;
  try {
    await appApi.startTest();
    startTime.value = formatDateTime(new Date());
    ElMessage.success('Test execution started');

    // 立即获取状态并开始轮询
    await fetchTestStatus();
    startStatusPolling();
  } catch (error) {
    console.error('启动测试失败:', error);
    ElMessage.error('Failed to start test execution');
  } finally {
    starting.value = false;
  }
};

// 暂停测试执行
const pauseTestExecution = async () => {
  pausing.value = true;
  try {
    await appApi.pauseTest();
    ElMessage.success('Test execution paused');

    // 立即更新状态
    await fetchTestStatus();
  } catch (error) {
    console.error('暂停测试失败:', error);
    ElMessage.error('Failed to pause test execution');
  } finally {
    pausing.value = false;
  }
};

// 恢复测试执行
const resumeTestExecution = async () => {
  resuming.value = true;
  try {
    await appApi.resumeTest();
    ElMessage.success('Test execution resumed');

    // 立即更新状态
    await fetchTestStatus();
  } catch (error) {
    console.error('恢复测试失败:', error);
    ElMessage.error('Failed to resume test execution');
  } finally {
    resuming.value = false;
  }
};

// 停止测试执行
const stopTestExecution = async () => {
  stopping.value = true;
  try {
    await appApi.stopTest();
    ElMessage.success('Test execution stopped');

    // 立即更新状态
    await fetchTestStatus();
  } catch (error) {
    console.error('停止测试失败:', error);
    ElMessage.error('Failed to stop test execution');
  } finally {
    stopping.value = false;
  }
};

// 获取测试状态
const fetchTestStatus = async () => {
  try {
    const response = await appApi.getTestStatus();
    runStatus.value = response.data;
    
    // 如果testResult为null，设置为Pending状态
    if (!runStatus.value.testResult) {
      runStatus.value.processState = ExecutionState.Pending;
    }
    
    lastUpdated.value = formatDateTime(new Date());

    // 使用工具函数判断测试是否完成
    if (isTesterCompleted(runStatus.value) && statusPollingTimer) {
      fetchSavedTestCasesCount();
      stopStatusPolling();
    }
  } catch (error) {
    console.error('获取测试状态失败:', error);
  }
};

// 开始状态轮询
const startStatusPolling = () => {
  // 清除可能存在的轮询定时器
  stopStatusPolling();
  statusPollingTimer = window.setInterval(fetchTestStatus, 500);
};

// 停止状态轮询
const stopStatusPolling = () => {
  if (statusPollingTimer) {
    clearInterval(statusPollingTimer);
    statusPollingTimer = null;
  }
};

// 组件挂载时获取保存的测试用例数量和测试状态
onMounted(() => {
  fetchSavedTestCasesCount();
  fetchTestStatus().then(() => {
    // 如果测试正在运行，开始轮询
    if (isRunning.value) {
      if (runStatus.value.testResult?.creationTime) {
        startTime.value = formatDateTime(new Date(runStatus.value.testResult.creationTime));
      } else {
        startTime.value = formatDateTime(new Date());
      }
      startStatusPolling();
    }
  });
});

// 组件卸载时停止轮询
onUnmounted(() => {
  stopStatusPolling();
});
</script>

<style scoped lang="scss">
.test-run-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 15px 20px;
}

/* 顶部工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  height: 30px;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
  }
}

/* 共用面板样式 */
.panel {
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

/* 内容区域样式 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
  gap: 12px;
  min-height: 0; /* 确保内容能正确滚动 */
}

/* 左右布局：进度和统计信息 */
.overview-layout {
  display: flex;
  gap: 12px;
  min-height: 0; /* 防止Flex项目过度增长 */
}

/* 左侧：进度圆环部分样式 */
.progress-section {
  flex: 0 0 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  
  .progress-circle-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    
    .test-state {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    /* 使用 :deep 选择器覆盖 el-progress 组件的样式，保持主题色不变 */
    :deep(.el-progress__text) {
      color: #606266 !important;
      
      &.font-large {
        font-size: 22px !important;
      }
      
      &.font-medium {
        font-size: 18px !important;
      }
      
      &.font-small {
        font-size: 15px !important;
      }
    }
    
    :deep(.el-progress-circle path:nth-child(2)) {
      stroke: var(--el-color-primary) !important;
    }
  }
}

/* 右侧：统计部分样式 */
.stats-section {
  flex: 1;
  min-width: 0; /* 防止内容溢出 */
}

/* 统计卡片网格样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 12px;
  height: 100%;
}

.stat-card {
  padding: 16px;
  display: flex;
  align-items: center;
  transition: transform 0.2s ease;

  .stat-icon {
    font-size: 32px;
    margin-right: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .stat-data {
    display: flex;
    flex-direction: column;
    
    .stat-value {
      font-size: 24px;
      font-weight: 600;
      line-height: 1.2;
    }
    
    .stat-label {
      font-size: 13px;
      color: #909399;
    }
  }
  
  &.success {
    .stat-icon, .stat-value {
      color: var(--el-color-success);
    }
    border-left: 3px solid var(--el-color-success);
  }
  
  &.failure {
    .stat-icon, .stat-value {
      color: var(--el-color-danger);
    }
    border-left: 3px solid var(--el-color-danger);
  }
  
  &.total {
    .stat-icon, .stat-value {
      color: var(--el-color-primary);
    }
    border-left: 3px solid var(--el-color-primary);
  }
  
  &.completion {
    .stat-icon, .stat-value {
      color: var(--el-color-warning);
    }
    border-left: 3px solid var(--el-color-warning);
  }
}

/* 底部信息区域样式 */
.footer-panel {
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  
  .timing-info {
    display: flex;
    gap: 16px;
    
    .time-item {
      display: flex;
      gap: 6px;
      font-size: 13px;
      
      .time-label {
        font-weight: 600;
        color: #606266;
      }
      
      .time-value {
        color: #303133;
      }
    }
  }
  
  .last-updated {
    color: #909399;
    font-size: 12px;
    font-style: italic;
  }
}

/* 当前操作信息面板 */
.operation-panel {
  padding: 12px 16px;
  margin-top: -6px;  /* 减少与上方面板的间距 */
  
  .current-operation {
    display: flex;
    gap: 8px;
    font-size: 13px;
    
    .operation-label {
      font-weight: 600;
      color: #606266;
    }
    
    .operation-value {
      color: #303133;
      word-break: break-word;  /* 确保长文本能够换行 */
    }
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .overview-layout {
    flex-direction: column;
  }
  
  .progress-section {
    flex: 0 0 auto;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
