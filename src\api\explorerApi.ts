import axios, { AxiosResponse } from 'axios'
import { USE_MOCK, mockApi } from '@/mock/mockApi'

const BASE_URL = '/api/explorer'

export const explorerApi = {
  // 选择保存文件夹
  selectFolder: (): Promise<AxiosResponse<string>> => {
    if (USE_MOCK) {
      return mockApi.explorer?.selectFolder() || Promise.reject(new Error('Mock API not implemented'));
    }
    return axios.get(`${BASE_URL}/select-folder`);
  },

  // 在资源管理器中打开指定路径
  openExplorer: (path: string): Promise<AxiosResponse<void>> => {
    if (USE_MOCK) {
      return mockApi.explorer?.openExplorer?.(path) || Promise.reject(new Error('Mock API not implemented'));
    }
    return axios.get(`${BASE_URL}/open-explorer`, { params: { path } });
  }
}

export default explorerApi
