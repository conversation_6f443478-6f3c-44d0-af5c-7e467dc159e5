<template>
  <div class="test-control-panel">
    <!-- 控制区域：按钮和状态合并为一行 -->
    <div class="control-header">
      <div class="action-buttons">
        <el-button type="primary" :icon="PlayArrow" @click="startTest"
          :disabled="disabled || testStatus.isRunning || startingTest" :loading="startingTest" size="default">
          Run
        </el-button>
        <el-button type="danger" :icon="Stop" @click="stopTest" :disabled="!testStatus.isRunning || stoppingTest"
          :loading="stoppingTest" size="default">
          Stop
        </el-button>
      </div>
      <div class="status-info" v-if="testStatusLoaded">
        <el-tag class="status-tag" :type="testStatus.isRunning ? 'success' : 'info'" effect="plain">
          {{ testStatus.isRunning ? 'Running' : 'Stopped' }}
        </el-tag>
        <span class="current-operation" v-if="testStatus.currentOperation">
          {{ testStatus.currentOperation }}
        </span>
      </div>
    </div>

    <!-- 进度条 -->
    <div class="progress-section" v-if="testStatusLoaded">
      <el-progress :percentage="testStatus.progress" :status="getProgressStatus()" :format="format => `${format}%`"
        :stroke-width="15"></el-progress>
    </div>

    <!-- 测试信息区域 -->
    <div class="info-grid" v-if="testStatusLoaded">
      <!-- 测试时间信息 -->
      <div class="info-row">
        <div class="info-item" v-if="testStatus.beginTime">
          <span class="label">Start:</span>
          <span class="value">{{ formatDateTime(testStatus.beginTime) }}</span>
        </div>
        <div class="info-item" v-if="testStatus.endTime">
          <span class="label">End:</span>
          <span class="value">{{ formatDateTime(testStatus.endTime) }}</span>
        </div>
      </div>
      
      <!-- 移除了测试摘要信息部分 -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, onUnmounted, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { PlayArrow, Stop } from '@element-plus/icons-vue';
import { interoperationApi, type InteroperationStatus } from '@/api/interoperationApi';

const props = defineProps<{
  disabled: boolean;
  testStatus: InteroperationStatus;
  testStatusLoaded: boolean;
}>();

const emit = defineEmits<{
  (e: 'statusUpdated', status: InteroperationStatus): void;
}>();

// 本地状态
const startingTest = ref(false);
const stoppingTest = ref(false);
let statusPollingInterval: number | null = null;

// 启动测试
const startTest = async () => {
  startingTest.value = true;
  try {
    const response = await interoperationApi.start();
    if (response.data.success) {
      ElMessage.success(response.data.message);
      console.log('Test started successfully');
      // 开始轮询状态
      startStatusPolling();
      // 立即更新一次状态
      await updateStatus();
    } else {
      ElMessage.error(response.data.message || 'Failed to start test');
    }
  } catch (error) {
    ElMessage.error('Failed to start test');
    console.error('Error starting test:', error);
  } finally {
    startingTest.value = false;
  }
};

// 停止测试
const stopTest = async () => {
  stoppingTest.value = true;
  try {
    const response = await interoperationApi.stop();
    if (response.data.success) {
      ElMessage.success(response.data.message);
      // 立即更新一次状态
      await updateStatus();
    } else {
      ElMessage.error(response.data.message || 'Failed to stop test');
    }
  } catch (error) {
    ElMessage.error('Failed to stop test');
    console.error('Error stopping test:', error);
  } finally {
    stoppingTest.value = false;
  }
};

// 获取测试状态
const updateStatus = async () => {
  try {
    const response = await interoperationApi.getStatus();
    console.log('Status updated:', response.data);
    emit('statusUpdated', { ...response.data }); // 使用对象解构创建新对象

    // 如果测试已停止，停止轮询
    if (!response.data.isRunning && statusPollingInterval !== null) {
      stopStatusPolling();
    }
  } catch (error) {
    console.error('Error updating test status:', error);
  }
};

// 开始定期轮询状态
const startStatusPolling = () => {
  if (statusPollingInterval !== null) {
    clearInterval(statusPollingInterval);
  }
  // 每秒更新一次状态，缩短更新间隔
  statusPollingInterval = window.setInterval(updateStatus, 1000);
};

// 停止状态轮询
const stopStatusPolling = () => {
  if (statusPollingInterval !== null) {
    clearInterval(statusPollingInterval);
    statusPollingInterval = null;
  }
};

// 格式化日期时间
const formatDateTime = (dateTimeStr: string | null) => {
  if (!dateTimeStr) return '';
  const date = new Date(dateTimeStr);
  return date.toLocaleString();
};

// 获取进度条状态
const getProgressStatus = () => {
  if (!props.testStatus.isRunning && props.testStatus.progress === 100) {
    return 'success';
  } else if (!props.testStatus.isRunning && props.testStatus.progress < 100) {
    return 'exception';
  }
  return '';
};

// 确保组件挂载时就开始检查状态
onMounted(async () => {
  // 如果测试已经在运行，启动轮询
  if (props.testStatus.isRunning) {
    startStatusPolling();
  }

  // 立即更新一次状态
  await updateStatus();
});

// 监听外部传入的testStatus变化
watch(() => props.testStatus.isRunning, (isRunning) => {
  if (isRunning && !statusPollingInterval) {
    startStatusPolling();
  } else if (!isRunning && statusPollingInterval) {
    stopStatusPolling();
  }
});

onUnmounted(() => {
  // 组件卸载时停止轮询
  stopStatusPolling();
});
</script>

<style scoped lang="scss">
.test-control-panel {
  width: 100%;
  padding-bottom: 10px;
}

.control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  
  .action-buttons {
    display: flex;
    gap: 8px;
    
    .el-button {
      min-width: 80px;
      font-weight: 500;
    }
  }
  
  .status-info {
    display: flex;
    align-items: center;
    gap: 10px;
    
    .status-tag {
      min-width: 60px;
      text-align: center;
    }
    
    .current-operation {
      font-size: 13px;
      color: var(--el-color-primary);
      max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.progress-section {
  margin-bottom: 12px;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  
  .info-item {
    display: flex;
    align-items: baseline;
    gap: 5px;
    
    .label {
      font-size: 12px;
      color: var(--el-text-color-secondary);
      font-weight: 500;
    }
    
    .value {
      font-size: 12px;
    }
  }
}

@media (max-width: 580px) {
  .control-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    
    .action-buttons {
      width: 100%;
      
      .el-button {
        flex: 1;
      }
    }
    
    .status-info {
      width: 100%;
      justify-content: space-between;
    }
  }
}
</style>
