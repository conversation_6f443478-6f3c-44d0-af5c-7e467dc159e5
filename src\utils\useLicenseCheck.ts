import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { licenseApi, type LicInfo } from '@/api/licenseApi'
import { formatDateTimeWithTimezone } from '@/utils/timeUtils'
import { 
  LicenseReminderStorage, 
  type ReminderThreshold
} from '@/utils/licenseReminderStorage'

/**
 * 授权检查组合式函数
 */
export function useLicenseCheck() {
  const router = useRouter()
  const isChecking = ref(false)

  /**
   * 检查授权并显示简单提示对话框
   */
  const checkLicenseWithPrompt = async (options?: {
    onCancel?: () => void
    onConfirm?: () => void
  }): Promise<{ hasValidLicense: boolean; licenseInfo?: LicInfo | null; reason?: string }> => {
    const { onCancel, onConfirm } = options || {}
    
    isChecking.value = true
    
    try {
      const result = await checkLicense()
      
      if (!result.hasValidLicense) {
        await showSimpleLicensePrompt(result.licenseInfo, result.reason, onCancel, onConfirm)
      } else if (result.licenseInfo) {
        // 检查是否需要显示到期提醒
        await checkExpirationReminder(result.licenseInfo)
      }
      
      return result
    } finally {
      isChecking.value = false
    }
  }

  /**
   * 静默检查授权
   */
  const checkLicenseSilent = async (): Promise<{ hasValidLicense: boolean; licenseInfo?: LicInfo | null; reason?: string }> => {
    return await checkLicense()
  }

  /**
   * 检查授权状态
   */
  const checkLicense = async (): Promise<{ hasValidLicense: boolean; licenseInfo: LicInfo | null; reason?: string }> => {
    try {
      const response = await licenseApi.getLicenseInfo()
      const licenseInfo = response.data
      
      if (!licenseInfo) {
        return {
          hasValidLicense: false,
          licenseInfo: null,
          reason: 'Failed to load license information'
        }
      }

      if (!licenseInfo.hasLicFile) {
        return {
          hasValidLicense: false,
          licenseInfo,
          reason: 'No license file found'
        }
      }

      if (licenseInfo.licFileInfo.isExpired) {
        return {
          hasValidLicense: false,
          licenseInfo,
          reason: `License expired on ${formatDateTimeWithTimezone(new Date(licenseInfo.licFileInfo.expirationTime))}`
        }
      }

      if (!licenseInfo.matchMachineCode) {
        return {
          hasValidLicense: false,
          licenseInfo,
          reason: 'License machine code does not match current machine'
        }
      }

      return {
        hasValidLicense: true,
        licenseInfo
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to check license:', error)
      return {
        hasValidLicense: false,
        licenseInfo: null,
        reason: 'Failed to load license information'
      }
    }
  }

  /**
   * 显示简单的授权提示对话框
   */
  const showSimpleLicensePrompt = async (
    licenseInfo: LicInfo | null, 
    reason?: string,
    onCancel?: () => void,
    onConfirm?: () => void
  ) => {
    let message = ''
    
    if (reason) {
      message += `${reason}\n\n`
    }
    
    try {
      await ElMessageBox.confirm(
        message,
        'License',
        {
          confirmButtonText: 'License Management',
          cancelButtonText: 'Cancel',
          type: 'warning',
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              router.push('/license')
              onConfirm?.()
            } else {
              onCancel?.()
            }
            done()
          }
        }
      )
    } catch (error) {
      onCancel?.()
    }
  }

  /**
   * 检查授权到期提醒
   */
  const checkExpirationReminder = async (licenseInfo: LicInfo): Promise<void> => {
    if (!licenseInfo.hasLicFile || !licenseInfo.licFileInfo) {
      return
    }

    const { licFileInfo } = licenseInfo
    const remainingDays = Math.ceil(licFileInfo.remainingSeconds / (60 * 60 * 24))
    
    // 检查是否需要显示提醒
    const requiredReminder = LicenseReminderStorage.getRequiredReminder(remainingDays, licFileInfo.id)
    
    if (requiredReminder) {
      await showExpirationReminder(licFileInfo, requiredReminder, remainingDays)
      // 标记这个提醒已显示
      LicenseReminderStorage.markReminderShown(licFileInfo.id, requiredReminder)
    }
  }

  /**
   * 显示授权到期提醒对话框（仅提示信息，只有确定按钮）
   */
  const showExpirationReminder = async (
    licFileInfo: LicInfo['licFileInfo'],
    threshold: ReminderThreshold,
    remainingDays: number
  ): Promise<void> => {
    const message = `Your license will expire in ${remainingDays} day${remainingDays > 1 ? 's' : ''}.\n\nExpiration time: ${formatDateTimeWithTimezone(new Date(licFileInfo.expirationTime))}`
    try {
      await ElMessageBox.alert(
        message,
        'License Expiration Reminder',
        {
          confirmButtonText: 'OK',
          type: 'warning'
        }
      )
    } catch (error) {
      // do nothing
    }
  }

  /**
   * 清空 license 提醒记录（在删除或上传新的 license 文件后调用）
   */
  const clearReminderRecords = (licenseId?: string): void => {
    if (licenseId) {
      LicenseReminderStorage.clearReminderRecord(licenseId)
    } else {
      LicenseReminderStorage.clearAllReminderRecords()
    }
  }

  return {
    isChecking,
    checkLicenseWithPrompt,
    checkLicenseSilent,
    clearReminderRecords
  }
}
