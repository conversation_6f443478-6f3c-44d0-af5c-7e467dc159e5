<template>
  <div class="sequence-setting-container" v-loading="loading">
    <!-- 左中右三栏布局 -->
    <div class="main-content">
      <!-- 左侧：Custom Package 列表 -->
      <div class="left-panel">
        <!-- 标题栏 -->
        <div class="panel-header">
          <h3 title="Custom Package">Custom Package</h3>
        </div>

        <!-- 自定义包列表 -->
        <div class="package-list-container">
          <div class="package-list">
            <div v-for="pkg in customPackageList" :key="pkg.key" class="package-item" :class="{
              'selected': pkg.key === selectedPackageKey
            }" @click="handlePackageSelect(pkg)">
              <div class="package-content">
                <div class="package-info">
                  <div class="package-tags" v-if="pkg.isActive">
                    <el-tag type="success" size="small" disable-transitions="true">Active</el-tag>
                  </div>
                  <div class="package-name" :title="pkg.displayName">{{ pkg.displayName }}</div>
                </div>
                <div class="package-actions">
                  <el-button type="danger" size="small" :icon="Delete" @click.stop="handleDeletePackage(pkg)" />
                </div>
              </div>
            </div>
            <el-empty v-if="customPackageList.length === 0" description="No custom packages" :image-size="100" />
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="bottom-buttons">
          <el-button type="primary" @click="handleCreateCustom">
            Create
          </el-button>
          <el-button @click="handleOpenTestSuite">
            Open Template
          </el-button>
        </div>
      </div>

      <!-- 中间：当前选中的 Package XML -->
      <div class="center-panel" v-if="selectedPackage || !previewPackage">
        <!-- 标题栏 -->
        <div class="panel-header" v-if="selectedPackage">
          <h3 :title="selectedPackage.displayName">{{ selectedPackage.displayName }}</h3>
        </div>

        <!-- XML编辑器 -->
        <div class="xml-editor-container">
          <XmlViewer v-if="selectedPackage" :title="selectedPackage.displayName" :content="xmlContent" :readonly="false"
            @update:content="handleXmlContentChange" />
          <el-empty v-else description="No package selected" :image-size="150" />
        </div>

        <!-- 底部保存按钮 -->
        <div v-if="selectedPackage" class="center-bottom-bar">
          <el-button type="primary" @click="handleSave">
            Save
          </el-button>
        </div>
      </div>

      <!-- 右侧：预览 Test Suite Package XML -->
      <div class="right-panel" v-if="previewPackage">
        <!-- 标题栏 -->
        <div class="panel-header">
          <h3 :title="previewPackage.displayName">{{ previewPackage.displayName }}</h3>
          <el-button type="text" :icon="Close" @click="handleClosePreview" />
        </div>

        <!-- XML预览 -->
        <div class="xml-preview-container">
          <XmlViewer :title="previewPackage.displayName" :content="previewXmlContent" :readonly="true" />
        </div>
      </div>
    </div>

    <!-- 创建自定义包对话框 -->
    <el-dialog v-model="isCreatingCustom" title="Create Sequence Package" width="600px">
      <el-form :model="createForm" label-width="140px">
        <el-form-item label="Based on" required>
          <el-cascader v-model="createForm.basedOn" :options="cascaderOptions" :props="cascaderProps"
            placeholder="Select base package" style="width: 100%" />
        </el-form-item>
        <el-form-item label="Package Name" required>
          <el-input v-model="createForm.customName" placeholder="Enter custom package name" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="handleCancelCreateCustom">Cancel</el-button>
        <el-button type="primary" @click="handleConfirmCreateCustom" :disabled="!isCreateFormValid">
          Create
        </el-button>
      </template>
    </el-dialog>

    <!-- Test Suite 选择对话框 -->
    <el-dialog v-model="isSelectingTestSuite" title="Open Template" width="600px">
      <el-form :model="previewForm" label-width="100px">
        <el-form-item label="Package" required>
          <el-cascader v-model="previewForm.selectedPackage" :options="cascaderOptions" :props="cascaderProps"
            placeholder="Select test suite package to preview" style="width: 100%" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="handleCancelSelectTestSuite">Cancel</el-button>
        <el-button type="primary" @click="handleConfirmSelectTestSuite" :disabled="!isPreviewFormValid">
          Preview
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Delete, Close } from '@element-plus/icons-vue';
import { testSuiteApi, type TestSuite, type SequencePackage } from '@/api/testSuiteApi';
import { sequenceApi, type SequenceConfigData } from '@/api/sequenceApi';
import { appApi } from '@/api/appApi';
import { testCaseCacheService } from '@/services/testCaseCacheService';
import XmlViewer from '@/components/TestSuite/XmlViewer.vue';

// 定义包项目的接口
interface PackageItem {
  key: string;
  isSuiteHeader?: boolean;
  suiteName?: string;
  displayName: string;
  isBuiltIn: boolean;
  isActive: boolean;
  testSuiteName: string;
  packageName: string;
  customName?: string;
  lastModified?: string;
  creationTime?: string;
}

const loading = ref(true);
const testSuites = ref<TestSuite[]>([]);
const customPackages = ref<SequencePackage[]>([]);
const selectedPackageKey = ref<string>('');
const xmlContent = ref('');
const isCreatingCustom = ref(false);
const isSelectingTestSuite = ref(false);

// 预览相关数据
const previewPackage = ref<PackageItem | null>(null);
const previewXmlContent = ref('');

// 创建表单
const createForm = ref({
  customName: '',
  basedOn: [] as string[]
});

// 预览表单
const previewForm = ref({
  selectedPackage: [] as string[]
});

// 当前配置
const config = ref<SequenceConfigData>({
  testSuiteName: '',
  sequencePackageName: '',
  isBuiltIn: true,
  customName: '',
  basePackageName: ''
});

// 级联选择器配置
const cascaderProps = {
  value: 'value',
  label: 'label',
  children: 'children'
};

// 级联选择器选项
const cascaderOptions = computed(() => {
  return testSuites.value.map(suite => ({
    value: suite.name,
    label: `${suite.name} v${suite.version}`,
    children: suite.packages.map(pkg => ({
      value: pkg.name,
      label: pkg.name
    }))
  }));
});

// 只显示自定义包的列表
const customPackageList = computed<PackageItem[]>(() => {
  const result: PackageItem[] = [];

  // 获取所有自定义包（按创建时间倒序，如果创建时间为空则按名称排序）
  const customPkgs = [...customPackages.value]
    .sort((a, b) => {
      // 如果两个包都有创建时间，按创建时间倒序排序
      if (a.creationTime && b.creationTime) {
        const timeA = new Date(a.creationTime).getTime();
        const timeB = new Date(b.creationTime).getTime();
        return timeB - timeA;
      }

      // 如果只有一个包有创建时间，有创建时间的排在前面
      if (a.creationTime && !b.creationTime) {
        return -1;
      }
      if (!a.creationTime && b.creationTime) {
        return 1;
      }

      // 如果两个包都没有创建时间（历史数据），按名称排序
      const nameA = a.customName || a.name || '';
      const nameB = b.customName || b.name || '';
      return nameA.localeCompare(nameB);
    });

  customPkgs.forEach(pkg => {
    const isActive = config.value.testSuiteName === pkg.testSuiteName &&
      config.value.customName === pkg.customName &&
      !config.value.isBuiltIn;

    result.push({
      key: `custom-${pkg.testSuiteName}-${pkg.customName}`,
      displayName: pkg.customName || pkg.name,
      isBuiltIn: false,
      isActive,
      testSuiteName: pkg.testSuiteName ?? '',
      packageName: pkg.name,
      customName: pkg.customName,
      lastModified: pkg.lastModified,
      creationTime: pkg.creationTime
    });
  });

  return result;
});

// 当前选中的包（只能是自定义包）
const selectedPackage = computed(() => {
  return customPackageList.value.find(pkg => pkg.key === selectedPackageKey.value);
});

// 创建表单是否有效
const isCreateFormValid = computed(() => {
  return createForm.value.customName.trim() && createForm.value.basedOn.length === 2;
});

// 预览表单是否有效
const isPreviewFormValid = computed(() => {
  return previewForm.value.selectedPackage.length === 2;
});

// 加载测试套件
const loadTestSuites = async () => {
  try {
    const response = await testSuiteApi.getBuiltIn();
    testSuites.value = response.data;
  } catch (error) {
    ElMessage.error('Failed to load test suites');
  }
};

// 加载所有自定义包
const loadAllCustomPackages = async () => {
  try {
    const allCustomPackages: any[] = [];

    // 为每个测试套件加载自定义包
    for (const suite of testSuites.value) {
      try {
        const response = await sequenceApi.getCustomPackages(suite.name);
        const packagesWithSuite = response.data.map((pkg: any) => ({
          ...pkg,
          testSuiteName: suite.name
        }));
        allCustomPackages.push(...packagesWithSuite);
      } catch (error) {
        console.error(`Failed to load custom packages for ${suite.name}:`, error);
      }
    }

    customPackages.value = allCustomPackages;
  } catch (error) {
    console.error('Failed to load custom packages:', error);
    customPackages.value = [];
  }
};

// 处理包选择（只处理自定义包）
const handlePackageSelect = async (pkg: PackageItem) => {
  selectedPackageKey.value = pkg.key;
  await loadPackageXml(pkg);
};

// 加载包的XML内容（只加载自定义包）
const loadPackageXml = async (pkg: PackageItem) => {
  try {
    // 自定义包：获取用户修改的XML
    const response = await testSuiteApi.getXml(pkg.testSuiteName, pkg.packageName, pkg.customName);
    xmlContent.value = response.data;
  } catch (error) {
    ElMessage.error('Failed to load XML content');
    console.error('Error loading XML:', error);
  }
};

// 处理XML内容变化
const handleXmlContentChange = (newContent: string) => {
  xmlContent.value = newContent;
};

// 加载配置
const loadConfig = async () => {
  loading.value = true;
  try {
    const response = await sequenceApi.getSequenceConfig();
    config.value = response.data;

    // 设置选中的包（只处理自定义包）
    if (config.value.testSuiteName && !config.value.isBuiltIn && config.value.customName) {
      selectedPackageKey.value = `custom-${config.value.testSuiteName}-${config.value.customName}`;
    }

    // 加载自定义包列表
    await loadAllCustomPackages();

    // 加载已选择包的XML内容
    if (selectedPackage.value) {
      await loadPackageXml(selectedPackage.value);
    }
  } catch (error) {
    ElMessage.error('Failed to load configuration');
  } finally {
    loading.value = false;
  }
};

// 生成默认名称的函数
const generateDefaultName = (basedOnPackageName: string): string => {
  if (!basedOnPackageName) return '';

  // 获取当前所有自定义包的名称
  const existingNames = customPackages.value.map(pkg => pkg.customName || '').filter(name => name);

  // 生成默认名称规则：packageName_N
  let counter = 1;
  let defaultName = `${basedOnPackageName}_${counter}`;

  // 找到第一个不存在的名称
  while (existingNames.includes(defaultName)) {
    counter++;
    defaultName = `${basedOnPackageName}_${counter}`;
  }

  return defaultName;
};

// 检查名称是否是自动生成的格式
const isAutoGeneratedName = (name: string, basedOnPackageName: string): boolean => {
  if (!name || !basedOnPackageName) return false;
  // 正则匹配：packageName_数字
  const pattern = new RegExp(`^${basedOnPackageName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}_\\d+$`);
  return pattern.test(name);
};

// 处理创建自定义包
const handleCreateCustom = () => {
  isCreatingCustom.value = true;
  createForm.value.customName = '';

  // 如果当前选中的是内建包，自动填充基于包
  if (selectedPackage.value && selectedPackage.value.isBuiltIn) {
    createForm.value.basedOn = [selectedPackage.value.testSuiteName, selectedPackage.value.packageName];
    // 生成默认名称
    createForm.value.customName = generateDefaultName(selectedPackage.value.packageName);
  } else {
    createForm.value.basedOn = [];
  }
};

// 确认创建自定义包
const handleConfirmCreateCustom = async () => {
  if (!isCreateFormValid.value) {
    ElMessage.warning('Please fill in all required fields');
    return;
  }

  // 检查是否有现有的测试用例
  try {
    const caseInfo = await appApi.getCaseCollectionInfo();
    if (caseInfo.data.caseCount > 0) {
      // 有用例时提示用户
      try {
        await ElMessageBox.confirm(
          `Creating a new sequence package will clear all ${caseInfo.data.caseCount} existing test cases. Do you want to continue?`,
          'Clear Test Cases Warning',
          {
            confirmButtonText: 'Continue and Clear Cases',
            cancelButtonText: 'Cancel',
            type: 'warning'
          }
        );

        // 用户确认后清空用例
        await appApi.clearCases();
        // 清空成功后清除所有缓存
        testCaseCacheService.clearCache();
      } catch {
        // 用户取消，不执行创建
        return;
      }
    }
  } catch (error) {
    console.error('Failed to check case collection info:', error);
    // 如果检查失败，继续创建但给出警告
    ElMessage.warning('Unable to check existing test cases, proceeding with creation');
  }

  const [testSuiteName, packageName] = createForm.value.basedOn;

  try {
    // 获取基础包的XML内容
    const response = await testSuiteApi.getXml(testSuiteName, packageName);

    const newConfig: SequenceConfigData = {
      testSuiteName,
      sequencePackageName: packageName,
      sequencePackageXml: response.data,
      isBuiltIn: false,
      customName: createForm.value.customName.trim(),
      basePackageName: packageName
    };

    await sequenceApi.updateSequenceConfig(newConfig);

    // 重新加载数据
    await loadAllCustomPackages();
    await loadConfig();

    // 选中新创建的包
    selectedPackageKey.value = `custom-${testSuiteName}-${createForm.value.customName.trim()}`;

    isCreatingCustom.value = false;
    ElMessage.success('Sequence package created successfully.');
  } catch (error) {
    console.error('Create sequence package failed:', error);
    ElMessage.error('Failed to create sequence package');
  }
};

// 取消创建自定义包
const handleCancelCreateCustom = () => {
  isCreatingCustom.value = false;
  createForm.value.customName = '';
  createForm.value.basedOn = [];
};

// 处理保存（保存XML并设为Active）
const handleSave = async () => {
  if (!selectedPackage.value) {
    ElMessage.warning('Please select a package');
    return;
  }

  if (!selectedPackage.value.customName) {
    ElMessage.warning('Please select a custom package');
    return;
  }

  // 检查是否有现有的测试用例
  try {
    const caseInfo = await appApi.getCaseCollectionInfo();
    if (caseInfo.data.caseCount > 0) {
      // 有用例时提示用户
      try {
        await ElMessageBox.confirm(
          `Saving the sequence configuration will clear all ${caseInfo.data.caseCount} existing test cases. Do you want to continue?`,
          'Clear Test Cases Warning',
          {
            confirmButtonText: 'Continue and Clear Cases',
            cancelButtonText: 'Cancel',
            type: 'warning'
          }
        );

        // 用户确认后清空用例
        await appApi.clearCases();
        // 清空成功后清除所有缓存
        testCaseCacheService.clearCache();
      } catch {
        // 用户取消，不执行保存
        return;
      }
    }
  } catch (error) {
    console.error('Failed to check case collection info:', error);
    // 如果检查失败，继续保存但给出警告
    ElMessage.warning('Unable to check existing test cases, proceeding with save');
  }

  loading.value = true;
  try {
    const newConfig: SequenceConfigData = {
      testSuiteName: selectedPackage.value.testSuiteName,
      sequencePackageName: selectedPackage.value.packageName,
      sequencePackageXml: xmlContent.value,
      isBuiltIn: false,
      customName: selectedPackage.value.customName,
      basePackageName: selectedPackage.value.packageName
    };

    await sequenceApi.updateSequenceConfig(newConfig);

    // 重新加载配置以更新Active状态
    await loadConfig();

    ElMessage.success('Package saved and set as active successfully.');
  } catch (error) {
    console.error('Save failed:', error);
    ElMessage.error('Failed to save package');
  } finally {
    loading.value = false;
  }
};

// 处理删除包（从列表项的删除按钮）
const handleDeletePackage = async (pkg: PackageItem) => {
  try {
    await ElMessageBox.confirm(
      `Are you sure you want to delete the package "${pkg.customName}"?`,
      'Delete Package',
      {
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }
    );

    await sequenceApi.deleteCustomSequencePackage(
      pkg.testSuiteName,
      pkg.customName!
    );

    // 重新加载数据
    await loadAllCustomPackages();
    await loadConfig();

    // 如果删除的是当前选中的包，清除选择
    if (selectedPackageKey.value === pkg.key) {
      selectedPackageKey.value = '';
      xmlContent.value = '';
    }

    ElMessage.success('Package deleted successfully.');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Delete failed:', error);
      ElMessage.error('Failed to delete package');
    }
  }
};

// 处理打开 Test Suite 对话框
const handleOpenTestSuite = () => {
  isSelectingTestSuite.value = true;
  previewForm.value.selectedPackage = [];
};

// 确认选择 Test Suite Package 进行预览
const handleConfirmSelectTestSuite = async () => {
  if (!isPreviewFormValid.value) {
    ElMessage.warning('Please select a test suite package');
    return;
  }

  const [testSuiteName, packageName] = previewForm.value.selectedPackage;

  try {
    // 获取内建包的XML内容
    const response = await testSuiteApi.getXml(testSuiteName, packageName);

    // 设置预览数据
    previewPackage.value = {
      key: `preview-${testSuiteName}-${packageName}`,
      displayName: `${testSuiteName} - ${packageName}`,
      isBuiltIn: true,
      isActive: false,
      testSuiteName,
      packageName
    };
    previewXmlContent.value = response.data;

    isSelectingTestSuite.value = false;
    ElMessage.success('Test suite package loaded for preview.');
  } catch (error) {
    console.error('Load preview failed:', error);
    ElMessage.error('Failed to load test suite package');
  }
};

// 取消选择 Test Suite
const handleCancelSelectTestSuite = () => {
  isSelectingTestSuite.value = false;
  previewForm.value.selectedPackage = [];
};

// 关闭预览
const handleClosePreview = () => {
  previewPackage.value = null;
  previewXmlContent.value = '';
};

// 监听 basedOn 变化，自动更新默认名称
watch(() => createForm.value.basedOn, (newBasedOn, oldBasedOn) => {
  if (!isCreatingCustom.value || !newBasedOn || newBasedOn.length !== 2) return;

  const [, newPackageName] = newBasedOn;
  const currentName = createForm.value.customName;

  // 如果当前名称为空，或者是基于旧包名自动生成的，则更新为新的默认名称
  if (!currentName || (oldBasedOn && oldBasedOn.length === 2 && isAutoGeneratedName(currentName, oldBasedOn[1]))) {
    createForm.value.customName = generateDefaultName(newPackageName);
  }
}, { deep: true });

onMounted(async () => {
  await loadTestSuites();
  await loadConfig();
});
</script>

<style scoped lang="scss">
.sequence-setting-container {
  display: flex;
  flex-direction: column;
  padding: 15px 20px;
  height: calc(100% - 30px);
  min-width: 0;
  flex: 1;
}

.main-content {
  display: flex;
  gap: 10px;
  height: 100%;
}

// 左侧面板 - Custom Package 列表
.left-panel {
  width: 300px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}

// 中间面板 - 当前选中的 Package XML
.center-panel {
  flex: 1;
  min-width: 0;
  /* 允许 flex 项目收缩到内容宽度以下 */
  display: flex;
  flex-direction: column;
}

// 右侧面板 - 预览 Test Suite Package XML
.right-panel {
  flex: 1;
  min-width: 0;
  /* 允许 flex 项目收缩到内容宽度以下 */
  display: flex;
  flex-direction: column;
  margin-bottom: 42px;
}

// 面板标题栏
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px 4px 0 0;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    margin-right: 8px;
    min-width: 0;
    /* 允许 h3 收缩到内容宽度以下 */
  }

  button {
    height: 20px;
  }
}

// 包列表容器
.package-list-container {
  flex: 1;
  background-color: #ffffff;
  border: 1px solid #e4e7ed;
  border-top: none;
  border-radius: 0 0 4px 4px;
  overflow: hidden;
}

.package-list {
  height: 100%;
  overflow-y: auto;
}

.package-item {
  padding: 10px 14px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f7fa;
  }

  &.selected {
    background-color: var(--el-border-color-extra-light);
    border-left: 3px solid var(--el-color-primary);
    padding-left: 13px;
  }

  &:last-child {
    border-bottom: none;
  }
}

.package-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  user-select: none;
}

.package-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
  height: 46px;
  justify-content: center;
}

.package-name {
  font-size: 13px;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 220px;
}

.package-tags {
  display: flex;
  gap: 6px;
}

.package-actions {
  margin-left: 10px;
}

// XML 编辑器容器
.xml-editor-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;

  :deep(.el-empty) {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin: 0;
  }
}

:deep(.editor-container) {
  border-radius: 0 0 4px 4px;
  border-top: none;
}

// XML 预览容器
.xml-preview-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

// 中间面板底部按钮
.center-bottom-bar {
  display: flex;
  justify-content: flex-start;
  margin-top: 10px;
}

// 左侧底部按钮
.bottom-buttons {
  margin-top: 10px;
  display: flex;
  gap: 10px;
  padding: 0;
}

:deep(.el-form-item:last-child) {
  margin-bottom: 0;
}
</style>
