<template>
  <div class="xml-viewer">
    <div class="editor-container">
      <XmlEditor :value="content" language="xml" :readOnly="readonly" @update:value="handleContentChange" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import XmlEditor from '@/components/XmlEditor/XmlEditor.vue'

defineProps<{
  title: string;
  content: string;
  readonly?: boolean;
}>()

const emit = defineEmits(['update:content']);

const handleContentChange = (value: string) => {
  emit('update:content', value);
}
</script>

<style scoped lang="scss">
.xml-viewer {
  display: flex;
  flex: 1;
  overflow: hidden;

  .viewer-card {
    height: 100%;
  }

  .viewer-header {
    h3 {
      margin: 0;
    }
  }

  .editor-container {
    display: flex;
    flex: 1;
  }
}

:deep(.el-card__body) {
  height: 96%;
  padding: 0;
}
</style>
