<template>
    <div class="license-view">
        <el-card class="license-card">
            <template #header>
                <div class="card-header">
                    <h2>License</h2>
                    <div class="header-actions">
                        <el-button type="primary" @click="showGetLicenseDialog">
                            <el-icon>
                                <Key />
                            </el-icon>
                            <span class="icon-button-text">Request License</span>
                        </el-button>

                        <el-button type="success" @click="handleAddLicense" :loading="uploading">
                            <el-icon>
                                <Plus />
                            </el-icon>
                            <span class="icon-button-text">Add License File</span>
                        </el-button>

                        <el-button type="danger" @click="handleRemoveLicense" :disabled="!licenseInfo?.hasLicFile || removing"
                            :loading="removing">
                            <el-icon>
                                <Delete />
                            </el-icon>
                            <span class="icon-button-text">Remove License</span>
                        </el-button>
                    </div>
                </div>
            </template>

            <!-- 许可证信息 -->
            <div class="license-section">
                <div v-if="licenseInfo?.hasLicFile" class="license-info">
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">License File:</span>
                            <span class="value">{{ licenseInfo.licFileInfo.fileName }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Status:</span>
                            <el-tag :type="getLicenseStatusType(licenseInfo)">
                                {{ getLicenseStatusText(licenseInfo) }}
                            </el-tag>
                        </div>
                        <div v-if="!licenseInfo.licFileInfo.isExpired" class="info-item">
                            <span class="label">Remaining:</span>
                            <span class="value remaining-time">
                                {{ formatRemainingSeconds(licenseInfo.licFileInfo.remainingSeconds) }}
                            </span>
                        </div>
                        <div class="info-item">
                            <span class="label">Expiration Time:</span>
                            <span class="value">{{ formatDateTimeWithTimezone(licenseInfo.licFileInfo.expirationTime) }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Issuer:</span>
                            <span class="value">{{ licenseInfo.licFileInfo.issuer }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Issue To:</span>
                            <span class="value">{{ licenseInfo.licFileInfo.audience }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Issue Time:</span>
                            <span class="value">{{ formatDateTimeWithTimezone(licenseInfo.licFileInfo.issueTime) }}</span>
                        </div>
                    </div>
                </div>

                <div v-else class="no-license">
                    <el-empty description="No license file found">
                        <template #image>
                            <el-icon size="60" color="#999">
                                <DocumentIcon />
                            </el-icon>
                        </template>
                    </el-empty>
                </div>
            </div>
        </el-card>

        <!-- 获取License对话框 -->
        <el-dialog v-model="getLicenseDialogVisible" title="Request License" width="600px">
            <div class="get-license-content">
                <div class="instruction">
                    <p>To request your license authorization, please:</p>
                    <ol>
                        <li>Copy the <strong>machine code</strong> below</li>
                        <li>Send your information and machine code to the <strong>contact email</strong></li>
                        <li>Wait for the <strong>license file(.lic)</strong> to be sent back to you</li>
                    </ol>
                </div>
                
                <div class="machine-code-section">
                    <div class="machine-code-header">
                        <span class="label">Machine Code:</span>
                        <el-button type="primary" size="small" @click="copyMachineCode" :icon="CopyDocument">
                            Copy
                        </el-button>
                    </div>
                    <div class="machine-code-display">
                        {{ licenseInfo?.machineCode || 'Loading...' }}
                    </div>
                </div>
                
                <div class="email-section">
                    <div class="email-header">
                        <span class="label">Contact Email:</span>
                        <el-button type="primary" size="small" @click="copyEmail" :icon="CopyDocument">
                            Copy
                        </el-button>
                    </div>
                    <div class="email-display">
                        <EMAIL>
                    </div>
                </div>
            </div>
            
            <template #footer>
                <el-button @click="getLicenseDialogVisible = false">Close</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete, Document as DocumentIcon, Key, CopyDocument, Message } from '@element-plus/icons-vue'
import { licenseApi, type LicInfo } from '@/api/licenseApi'
import { formatDateTimeWithTimezone, formatRemainingSeconds } from '@/utils/timeUtils'
import { licenseEvents } from '@/utils/eventBus'

const licenseInfo = ref<LicInfo | null>(null)
const loading = ref(false)
const uploading = ref(false)
const removing = ref(false)
const getLicenseDialogVisible = ref(false)

// 获取许可证信息
const getLicenseInfo = async () => {
    try {
        loading.value = true
        const response = await licenseApi.getLicenseInfo()
        licenseInfo.value = response.data
    } catch (error: any) {
        console.error('Failed to get license info:', error)
        ElMessage.error(error.response?.data?.Message || 'Failed to load license information')
    } finally {
        loading.value = false
    }
}

// 显示获取License对话框
const showGetLicenseDialog = () => {
    getLicenseDialogVisible.value = true
}

// 复制机器码
const copyMachineCode = async () => {
    if (!licenseInfo.value?.machineCode) {
        ElMessage.error('Machine code not available')
        return
    }
    
    try {
        await navigator.clipboard.writeText(licenseInfo.value.machineCode)
        ElMessage.success('Machine code copied to clipboard')
    } catch (error) {
        // 如果clipboard API不可用，尝试使用传统方法
        try {
            const textArea = document.createElement('textarea')
            textArea.value = licenseInfo.value.machineCode
            document.body.appendChild(textArea)
            textArea.select()
            document.execCommand('copy')
            document.body.removeChild(textArea)
            ElMessage.success('Machine code copied to clipboard')
        } catch (fallbackError) {
            ElMessage.error('Failed to copy machine code')
        }
    }
}

// 复制邮箱地址
const copyEmail = async () => {
    const email = '<EMAIL>'
    
    try {
        await navigator.clipboard.writeText(email)
        ElMessage.success('Email address copied to clipboard')
    } catch (error) {
        // 如果clipboard API不可用，尝试使用传统方法
        try {
            const textArea = document.createElement('textarea')
            textArea.value = email
            document.body.appendChild(textArea)
            textArea.select()
            document.execCommand('copy')
            document.body.removeChild(textArea)
            ElMessage.success('Email address copied to clipboard')
        } catch (fallbackError) {
            ElMessage.error('Failed to copy email address')
        }
    }
}

// 添加许可证
const handleAddLicense = async () => {
    try {
        uploading.value = true
        
        // 使用服务端文件选择器选择并上传许可证
        const uploadResponse = await licenseApi.selectAndUploadLicense()
        
        licenseInfo.value = uploadResponse.data
        
        // 通知其他组件license已更新（会自动清理提醒记录）
        licenseEvents.notifyLicenseUpdated()
        
        ElMessage.success('License added successfully')
    } catch (error: any) {
        if (error.response?.data?.message === 'UserCanceled') {
            return // 用户取消选择，不显示错误
        }
        if (error.response?.data?.message === 'InvalidFileFormat') {
            ElMessage.error('Invalid license file format')
            return
        }
        console.error('Failed to add license file:', error)
        ElMessage.error(error.response?.data?.Message || 'Failed to add license file')
    } finally {
        uploading.value = false
    }
}

// 移除许可证
const handleRemoveLicense = async () => {
    if (!licenseInfo.value?.hasLicFile) return

    try {
        await ElMessageBox.confirm(
            'Are you sure you want to remove the current license?',
            'Remove License',
            {
                confirmButtonText: 'Remove',
                cancelButtonText: 'Cancel',
                type: 'warning',
            }
        )

        const currentLicenseId = licenseInfo.value.licFileInfo.id
        
        removing.value = true
        await licenseApi.deleteLicense({
            licenseId: currentLicenseId
        })

        // 通知其他组件license已删除（会自动清理提醒记录）
        licenseEvents.notifyLicenseDeleted()

        ElMessage.success('License removed successfully')
        await getLicenseInfo() // 刷新信息
    } catch (error: any) {
        if (error === 'cancel') {
            return // 用户取消
        }
        console.error('Failed to remove license:', error)
        ElMessage.error(error.response?.data?.Message || 'Failed to remove license')
    } finally {
        removing.value = false
    }
}

// 获取许可证状态类型
const getLicenseStatusType = (licInfo: LicInfo): string => {
    if (!licInfo.hasLicFile) {
        return 'info'
    }
    if (licInfo.licFileInfo.isExpired) {
        return 'danger'
    }
    if (!licInfo.matchMachineCode) {
        return 'danger'
    }
    return 'success'
}

// 获取许可证状态文本
const getLicenseStatusText = (licInfo: LicInfo): string => {
    if (!licInfo.hasLicFile) {
        return 'No License'
    }
    if (licInfo.licFileInfo.isExpired) {
        return 'Expired'
    }
    if (!licInfo.matchMachineCode) {
        return 'Machine Code Mismatch'
    }
    return 'Valid'
}

onMounted(() => {
    getLicenseInfo()
})
</script>

<style scoped>
.license-view {
    display: flex;
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;
}

.license-card {
    margin: 0 auto;
    margin: 20px;
    display: flex;
    flex: 1;
    flex-direction: column;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    margin: 0;
    color: #333;
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.license-section {
    margin-bottom: 30px;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.info-item {
    display: flex;
    align-items: flex-start;
    padding: 8px 0;
}

.info-item .label {
    font-weight: 500;
    color: #666;
    min-width: 120px;
    margin-right: 8px;
}

.info-item .value {
    color: #333;
    word-break: break-all;
}

.no-license {
    text-align: center;
    padding: 40px 0;
}

/* Get License Dialog Styles */
.get-license-content {
    padding: 0 0 20px 0;
}

.instruction {
    margin-bottom: 20px;
}

.instruction p {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 14px;
}

.instruction ol {
    margin: 0;
    padding-left: 20px;
}

.instruction li {
    margin-bottom: 5px;
    color: #666;
    font-size: 14px;
}

.machine-code-section {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.machine-code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.machine-code-header .label {
    font-weight: 500;
    color: #333;
}

.machine-code-display {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    color: #495057;
    background-color: #fff;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    word-break: break-all;
    line-height: 1.4;
}

.email-section {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.email-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.email-header .label {
    font-weight: 500;
    color: #333;
}

.email-display {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    color: #495057;
    background-color: #fff;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    word-break: break-all;
    line-height: 1.4;
}

.icon-button-text {
    margin-left: 5px;
}
</style>
