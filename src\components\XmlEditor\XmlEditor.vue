<template>
  <div ref="editorContainer" :class="['editor-container', { 'app-editor-readonly': readOnly }]" class=""></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, defineProps, defineEmits } from 'vue'
import { EditorState, Compartment } from '@codemirror/state'
import { EditorView } from '@codemirror/view'
import { defaultKeymap } from '@codemirror/commands'
import {
  lineNumbers,
  highlightActiveLineGutter,
  highlightSpecialChars,
  drawSelection,
  dropCursor
} from "@codemirror/view"

import { xml } from '@codemirror/lang-xml'
import {
  indentOnInput,
  syntaxHighlighting,
  defaultHighlightStyle,
  foldGutter
} from "@codemirror/language"
import { history, historyKeymap } from "@codemirror/commands"
import { foldKeymap } from "@codemirror/language"
import { keymap } from "@codemirror/view"

const props = defineProps<{
  value: string
  language?: string
  readOnly?: boolean
}>()

const emit = defineEmits(['update:value', 'change'])

const editorContainer = ref<HTMLElement | null>(null)
let view: EditorView | null = null;

// 创建可重新配置的 compartments
const readOnlyCompartment = new Compartment();

// 创建一个基础设置，代替 basicSetup
const basicSetupExtensions = [
  lineNumbers(),
  highlightActiveLineGutter(),
  highlightSpecialChars(),
  history(),
  drawSelection(),
  dropCursor(),
  EditorState.allowMultipleSelections.of(true),
  indentOnInput(),
  syntaxHighlighting(defaultHighlightStyle, { fallback: true }),
  foldGutter(),
  keymap.of([
    ...defaultKeymap,
    ...historyKeymap,
    ...foldKeymap
  ])
]

// 创建编辑器实例
const createEditor = () => {
  if (!editorContainer.value) return

  // 使用重新构建的扩展集
  const extensions = [
    ...basicSetupExtensions,
    xml(),
    EditorView.updateListener.of(update => {
      if (update.docChanged) {
        const value = update.state.doc.toString()
        emit('update:value', value)
        emit('change', value)
      }
    }),
    readOnlyCompartment.of(EditorState.readOnly.of(props.readOnly))
  ]

  // 创建编辑器状态
  const state = EditorState.create({
    doc: props.value,
    extensions
  })

  // 创建编辑器视图
  view = new EditorView({
    state,
    parent: editorContainer.value
  })
}

// 当组件挂载时创建编辑器
onMounted(() => {
  createEditor()
})

// 监听值的变化
watch(() => props.value, (newValue) => {
  if (view && newValue !== view.state.doc.toString()) {
    const currentScrollPos = view?.scrollDOM.scrollTop || 0
    view.dispatch({
      changes: {
        from: 0,
        to: view.state.doc.length,
        insert: newValue
      }
    })
    // 保持滚动位置
    setTimeout(() => {
      if (view) view.scrollDOM.scrollTop = currentScrollPos
    }, 0)
  }
})

// 监听只读状态的变化
watch(() => props.readOnly, (newReadonly) => {
  if (view) {
    view.dispatch({
      effects: readOnlyCompartment.reconfigure(EditorState.readOnly.of(newReadonly))
    });
  }
});


// 在组件卸载前销毁编辑器
onBeforeUnmount(() => {
  if (view) {
    view.destroy()
    view = null
  }
})
</script>

<style scoped>
.editor-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

:deep(.cm-editor) {
  display: flex;
  flex: 1;
  font-family: 'Courier New', Courier, monospace;
  font-size: 13px;
}

:deep(.cm-scroller) {
  overflow: auto;
}

:deep(.cm-content) {
  white-space: pre-wrap;
  word-break: normal;
  word-wrap: break-word;
}

:deep(.cm-content[contenteditable=false]) {
  background-color: #f5f7fa;
  cursor: text;
  /* 确保显示文本选择光标 */
}

/* 允许在只读模式下选择文本 */
:deep(.readonly-but-selectable) {
  user-select: text !important;
  /* 确保文本可以被选择 */
  cursor: text !important;
  /* 显示文本选择光标 */
}

:deep(.cm-editor.cm-focused) {
  outline: none;
  /* 移除聚焦时的轮廓 */
}
</style>
