import { mockSuccess } from '../mockApi';
import { AxiosResponse } from 'axios';
import { ElMessage } from 'element-plus';

// 文件资源管理器 API 模拟实现
export const mockExplorerApi = {
  // 模拟选择文件夹操作
  selectFolder: (): Promise<AxiosResponse<string>> => {
    // 返回一个模拟的文件夹路径
    return mockSuccess('D:\\mock\\selected\\folder\\path');
  },

  // 模拟在资源管理器中打开指定路径
  openExplorer: (path: string): Promise<AxiosResponse<void>> => {
    ElMessage.success(`[模拟] 已在资源管理器中打开路径: ${path}`);
    return mockSuccess(undefined);
  }
};
