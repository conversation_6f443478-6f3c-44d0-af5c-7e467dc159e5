<template>
  <div 
    class="custom-checkbox-wrapper"
    :class="[`status-${statusType.toLowerCase()}-style`, { 'disabled': disabled }]"
    @click="!disabled && toggleCheckbox()"
  >
    <div class="checkbox-container">
      <div class="checkbox-box">
        <input 
          type="checkbox" 
          :id="`checkbox-${id}`"
          :disabled="disabled"
          v-model="isChecked"
          class="custom-checkbox-input"
          @change="onCheckboxChange"
        />
        <span class="checkmark"></span>
      </div>
      <label class="custom-checkbox-label" @click.stop>
        <slot>{{ label }}</slot>
      </label>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, watch } from 'vue';

const props = defineProps<{
  modelValue: boolean;
  statusType: string; // 'Pending', 'Running', 'Success', 'Failure'
  label?: string;
  disabled?: boolean;
  id: string | number;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
}>();

// 使用本地状态跟踪选中状态
const isChecked = ref(props.modelValue);

// 监听外部模型值变化
watch(() => props.modelValue, (newValue) => {
  isChecked.value = newValue;
});

// 处理checkbox变化
const onCheckboxChange = () => {
  emit('update:modelValue', isChecked.value);
};

// 手动切换checkbox状态
const toggleCheckbox = () => {
  if (props.disabled) return;
  isChecked.value = !isChecked.value;
  emit('update:modelValue', isChecked.value);
};
</script>

<style scoped lang="scss">
.custom-checkbox-wrapper {
  cursor: pointer;
  border-radius: 3px;
  padding: 2px;
  border-width: 1px;
  user-select: none;
  display: inline-flex;
  align-items: center;
  transition: background-color 0.15s ease; 
  width: fit-content; // 确保宽度适应内容
  
  &:hover:not(.disabled) {
    opacity: 0.95;
  }
  
  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  // 按状态定义样式 - 修改Pending状态为蓝色
  &.status-pending-style {
    border-color: var(--el-color-primary); // 从info改为primary
    
    .custom-checkbox-label {
      color: var(--el-color-primary); // 从info改为primary
    }
    
    .checkbox-box {
      border-color: var(--el-color-primary); // 从info改为primary
    }
    
    .checkmark::after {
      border-color: var(--el-color-primary); // 从info改为primary
    }
    
    &:hover:not(.disabled) {
      background-color: rgba(var(--el-color-primary-rgb), 0.1); // 从info改为primary
    }
  }
  
  &.status-running-style {
    border-color: var(--el-color-warning);
    
    .custom-checkbox-label {
      color: var(--el-color-warning);
    }
    
    .checkbox-box {
      border-color: var(--el-color-warning);
    }
    
    .checkmark::after {
      border-color: var(--el-color-warning);
    }
    
    &:hover:not(.disabled) {
      background-color: rgba(var(--el-color-warning-rgb), 0.1);
    }
  }
  
  &.status-success-style {
    border-color: var(--el-color-success);
    
    .custom-checkbox-label {
      color: var(--el-color-success);
    }
    
    .checkbox-box {
      border-color: var(--el-color-success);
    }
    
    .checkmark::after {
      border-color: var(--el-color-success);
    }
    
    &:hover:not(.disabled) {
      background-color: rgba(var(--el-color-success-rgb), 0.2);
    }
  }
  
  &.status-failure-style {
    border-color: var(--el-color-danger);
    
    .custom-checkbox-label {
      color: var(--el-color-danger);
    }
    
    .checkbox-box {
      border-color: var(--el-color-danger);
    }
    
    .checkmark::after {
      border-color: var(--el-color-danger);
    }
    
    &:hover:not(.disabled) {
      background-color: rgba(var(--el-color-danger-rgb), 0.1);
    }
  }
}

.checkbox-container {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 2px 3px;
  pointer-events: none; // 防止容器阻止点击事件冒泡到wrapper
}

.checkbox-box {
  display: inline-block;
  position: relative;
  width: 14px; // 减小尺寸
  height: 14px; // 减小尺寸
  border: 1px solid currentColor;
  border-radius: 2px; // 稍微减小边框圆角
  margin-right: 6px;
  background-color: transparent;
}

.custom-checkbox-input {
  position: absolute;
  opacity: 0;
  height: 0;
  width: 0;
  pointer-events: none; // 禁用原生input的鼠标事件
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  pointer-events: none; // 确保checkmark不会捕获点击事件
  
  &::after {
    content: "";
    position: absolute;
    display: none;
    left: 4px; // 调整勾的位置以适应更小的盒子
    top: 0px;  // 调整勾的位置以适应更小的盒子
    width: 4px;
    height: 8px;
    border: solid currentColor;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }
}

.custom-checkbox-input:checked ~ .checkmark::after {
  display: block;
}

.custom-checkbox-label {
  white-space: nowrap;
  font-size: 12px;
  pointer-events: none; // 确保标签不会捕获点击事件
}
</style>
