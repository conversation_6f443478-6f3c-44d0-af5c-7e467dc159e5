<template>
  <div class="hardware-config-container" v-loading="isLoading">
    <!-- 中间空白区域 -->
    <div class="middle-panel">
      <el-form :model="form" label-position="top" label-width="160px">
        <el-form-item label="Bus Type">
          <el-radio-group v-model="form.communicationType" @change="handleCommunicationTypeChange">
            <el-radio :label="'Can'">CAN</el-radio>
            <el-radio :label="'CanFd'">CANFD</el-radio>
          </el-radio-group>
        </el-form-item>

        <template v-if="form.communicationType === 'Can'">
          <div class="form-item-with-tag">
            <el-form-item label="Device Channel" class="form-item">
              <device-channel-select v-model="form.canConfig.deviceChannelName" :devices="canDevices" />
            </el-form-item>
            <el-tag :key="updateTimestamp" class="device-status-tag" size="small"
              :type="getDeviceConnectStatus(form.canConfig.deviceChannelName).isConnected ? 'success' : 'danger'">
              <el-icon class="status-icon">
                <component
                  :is="getDeviceConnectStatus(form.canConfig.deviceChannelName).isConnected ? 'Connection' : 'CircleClose'" />
              </el-icon>
              <span class="status-text">{{ getDeviceConnectStatus(form.canConfig.deviceChannelName).isConnected ?
                'Connected' : 'Disconnected' }}</span>
            </el-tag>
          </div>

          <el-form-item label="Baud Rate" class="form-item">
            <el-select v-model="form.canConfig.dataBitrate" placeholder="Select Baud Rate" style="width: 100%">
              <el-option v-for="rate in baudRates" :key="rate" :label="`${rate / 1000} kbit/s`" :value="rate">
              </el-option>
            </el-select>
          </el-form-item>
        </template>

        <template v-else>
          <div class="form-item-with-tag">
            <el-form-item label="Device Channel" class="form-item">
              <device-channel-select v-model="form.canFdConfig.deviceChannelName" :devices="canFdDevices" />
            </el-form-item>
            <el-tag class="device-status-tag" size="small"
              :type="getDeviceConnectStatus(form.canFdConfig.deviceChannelName).isConnected ? 'success' : 'danger'">
              <el-icon class="status-icon">
                <component
                  :is="getDeviceConnectStatus(form.canFdConfig.deviceChannelName).isConnected ? 'Connection' : 'CircleClose'" />
              </el-icon>
              <span class="status-text">{{ getDeviceConnectStatus(form.canFdConfig.deviceChannelName).isConnected ?
                'Connected' : 'Disconnected' }}</span>
            </el-tag>
          </div>

          <el-form-item label="Arbitration Phase Baud Rate" class="form-item">
            <el-select v-model="form.canFdConfig.arbitrationBitrate" placeholder="Select Arbitration Phase Baud Rate"
              style="width: 100%">
              <el-option v-for="rate in baudRates" :key="rate" :label="`${rate / 1000} kbit/s`" :value="rate">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="Data Phase Baud Rate" class="form-item">
            <el-select v-model="form.canFdConfig.dataBitrate" placeholder="Select Data Phase Baud Rate"
              style="width: 100%">
              <el-option v-for="rate in fdDataRates" :key="rate" :label="`${rate / 1000} kbit/s`" :value="rate">
              </el-option>
            </el-select>
          </el-form-item>
        </template>
      </el-form>
    </div>

    <!-- 底部按钮区域 -->
    <div class="bottom-panel">
      <el-button type="primary" @click="handleSave" :loading="isSaving">Save</el-button>
    </div>
  </div>
</template>

<script lang="ts">
import {
  ref,
  computed,
  onMounted,
  onUnmounted,
  watch,
  nextTick,
  defineComponent
} from 'vue';
import {
  Connection,
  CircleClose
} from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { hardwareApi } from '@/api/hardwareApi';
import DeviceChannelSelect from './DeviceChannelSelect.vue';

export default defineComponent({
  components: {
    DeviceChannelSelect,
    Connection,
    CircleClose
  },

  props: {
    testPlanId: {
      type: String,
      required: false
    }
  },

  setup(props) {
    // 添加更新时间戳
    const updateTimestamp = ref(Date.now());

    // 表单数据
    const form = ref({
      communicationType: 'Can',
      canConfig: {
        deviceChannelName: '',
        dataBitrate: 500000
      },
      canFdConfig: {
        deviceChannelName: '',
        arbitrationBitrate: 500000,
        dataBitrate: 2000000
      }
    });

    // 保存状态管理 - 简化版本
    const originalForm = ref({});
    const isSaving = ref(false);

    // 状态管理
    const isLoading = ref(false);
    const isRefreshing = ref(false);
    const hardwareConfig = ref<any>(null);
    const deviceChannels = ref<any[]>([]);
    const lastDeviceChannels = ref<any[]>([]);

    // 添加缺失的响应式变量
    const baudRates = ref([125000, 250000, 500000, 1000000]);
    const fdDataRates = ref([1000000, 2000000, 4000000, 8000000]);

    // 添加计算属性
    const canDevices = computed(() => {
      return deviceChannels.value.filter(device =>
        device.communicationType === 'Can' || device.communicationType === 'CanFd');
    });

    const canFdDevices = computed(() => {
      return deviceChannels.value.filter(device =>
        device.communicationType === 'CanFd');
    });

    // 轮询计时器和状态
    let pollingTimer: any = null;
    let pollingActive = true;

    // 处理表单值变化
    const handleValueChange = () => {
      handleCommunicationTypeChange();
    };

    // 保存配置
    const handleSave = async () => {
      try {
        isSaving.value = true;

        const configToSave = {
          communicationType: form.value.communicationType,
          canConfig: form.value.communicationType === 'Can' ? form.value.canConfig : undefined,
          canFdConfig: form.value.communicationType === 'CanFd' ? form.value.canFdConfig : undefined
        };

        await hardwareApi.updateHardwareConfig(configToSave);

        // 更新原始表单状态
        originalForm.value = JSON.parse(JSON.stringify(form.value));
        ElMessage.success('Hardware settings saved.');
      } catch (error) {
        console.error('Failed to save hardware config:', error);
        ElMessage.error('Failed to save hardware configuration');
      } finally {
        isSaving.value = false;
      }
    };

    // 加载硬件配置
    const loadHardwareConfig = async () => {
      if (!pollingActive) return;

      try {
        isLoading.value = true;
        const response = await hardwareApi.getHardwareConfig();

        // 检查设备列表是否有变化
        const newDeviceChannels = response.data.deviceChannels || [];
        const devicesChanged = hasDeviceListChanged(lastDeviceChannels.value, newDeviceChannels);

        if (devicesChanged) {
          deviceChannels.value = newDeviceChannels;
          lastDeviceChannels.value = JSON.parse(JSON.stringify(newDeviceChannels));
          updateTimestamp.value = Date.now();
        }

        // 更新配置 - 移除hasUnsavedChanges检查
        if (response.data.testPlanConfig) {
          const newConfig = response.data.testPlanConfig;
          const configChanged = hasConfigChanged(hardwareConfig.value, newConfig);

          if (configChanged) {
            hardwareConfig.value = newConfig;
            form.value = {
              communicationType: newConfig.communicationType,
              canConfig: {
                ...form.value.canConfig,
                ...newConfig.canConfig
              },
              canFdConfig: {
                ...form.value.canFdConfig,
                ...newConfig.canFdConfig
              }
            };
            originalForm.value = JSON.parse(JSON.stringify(form.value));
          }
        }
      } catch (error) {
        console.error('Failed to load hardware config:', error);
        ElMessage.error('Failed to load hardware configuration');
      } finally {
        isLoading.value = false;
      }
    };

    // 比较设备列表是否有变化
    const hasDeviceListChanged = (oldDevices: any[], newDevices: any[]): boolean => {
      if (!oldDevices || !newDevices) return true;
      if (oldDevices.length !== newDevices.length) return true;

      // 比较名称和连接状态
      const oldMap = new Map(oldDevices.map(d => [d.name, d.isConnected]));
      return newDevices.some(d => oldMap.get(d.name) !== d.isConnected);
    };

    // 比较配置是否有变化
    const hasConfigChanged = (oldConfig: any, newConfig: any): boolean => {
      if (!oldConfig && newConfig) return true;
      if (oldConfig && !newConfig) return true;
      if (!oldConfig && !newConfig) return false;

      try {
        // 使用JSON字符串比较进行浅比较
        const oldConfigStr = JSON.stringify(oldConfig);
        const newConfigStr = JSON.stringify(newConfig);
        return oldConfigStr !== newConfigStr;
      } catch (e) {
        // 如果JSON序列化失败，回退到按属性比较
        return oldConfig.communicationType !== newConfig.communicationType ||
          JSON.stringify(oldConfig.canConfig) !== JSON.stringify(newConfig.canConfig) ||
          JSON.stringify(oldConfig.canFdConfig) !== JSON.stringify(newConfig.canFdConfig);
      }
    };

    // 硬件配置选项 - 使用固定值代替API调用
    const initHardwareOptions = () => {
      // 使用固定的波特率值
      baudRates.value = [125000, 250000, 500000, 1000000];
      fdDataRates.value = [1000000, 2000000, 4000000, 8000000];
    };

    // 处理总线类型切换
    const handleCommunicationTypeChange = () => {
      // 如果用户从CanFd切换到Can，需要检查并调整当前选择
      if (form.value.communicationType === 'Can') {
        // 如果之前选择的设备不支持普通Can，则清空选择
        const currentDevice = deviceChannels.value.find(
          d => d.name === form.value.canConfig.deviceChannelName
        );
        if (currentDevice && currentDevice.communicationType !== 'Can' && currentDevice.communicationType !== 'CanFd') {
          form.value.canConfig.deviceChannelName = '';
        }
      } else {
        // 如果从Can切换到CanFd，检查设备是否支持CanFd
        const currentDevice = deviceChannels.value.find(
          d => d.name === form.value.canFdConfig.deviceChannelName
        );
        if (currentDevice && currentDevice.communicationType !== 'CanFd') {
          form.value.canFdConfig.deviceChannelName = '';
        }
      }
    };

    // 将方法改为计算属性
    const getDeviceConnectStatus = computed(() => {
      return (deviceName: string) => {
        const device = deviceChannels.value.find(d => d.name === deviceName);
        return {
          isConnected: device?.isConnected || false
        };
      };
    });

    // 获取设备标签，显示连接状态
    const getDeviceLabel = (device: any) => {
      return `${device.name} ${device.isConnected ? '(Connected)' : '(Disconnected)'}`;
    };

    // 刷新设备列表
    const refreshDevices = async () => {
      try {
        isRefreshing.value = true;
        pollingActive = true;
        await loadHardwareConfig();
        ElMessage.success('Device list refreshed');
      } finally {
        isRefreshing.value = false;
      }
    };

    // 启动设备轮询
    const startDevicePolling = () => {
      pollingTimer = setInterval(() => {
        loadHardwareConfig();
      }, 2000); // 固定2秒轮询
    };

    // 停止设备轮询
    const stopDevicePolling = () => {
      pollingActive = false; // 标记轮询为非活跃
      if (pollingTimer) {
        clearInterval(pollingTimer);
        pollingTimer = null;
      }
    };

    // 组件挂载时加载配置
    onMounted(async () => {
      initHardwareOptions();
      await loadHardwareConfig();

      nextTick(() => {
        originalForm.value = JSON.parse(JSON.stringify(form.value));
      });

      startDevicePolling();
    });

    // 组件卸载时停止轮询
    onUnmounted(() => {
      stopDevicePolling();
    });

    // 当测试计划ID改变时重新加载配置
    watch(() => props.testPlanId, async (newVal) => {
      if (newVal) {
        await loadHardwareConfig();

        nextTick(() => {
          originalForm.value = JSON.parse(JSON.stringify(form.value));
        });
      }
    });

    return {
      form,
      isLoading,
      isRefreshing,
      isSaving,
      canDevices,
      canFdDevices,
      baudRates,
      fdDataRates,
      handleCommunicationTypeChange,
      handleValueChange,
      handleSave,
      getDeviceConnectStatus,
      getDeviceLabel,
      refreshDevices,
      updateTimestamp,
    };
  }
});
</script>

<style scoped lang="scss">
.hardware-config-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 100%;
  width: 100%;
}

.middle-panel {
  flex: 1;
  min-height: 0;
  background-color: #ffffff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border-radius: 2px;

  .form-item {
    margin-bottom: 20px;
    max-width: 400px;
  }

  .form-item-with-tag {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    margin-bottom: 20px;

    .form-item {
      flex: 1;
      margin-bottom: 0;
    }

    .device-status-tag {
      margin-bottom: 8px;
    }
  }
}

.bottom-panel {
  background-color: #ffffff;
  border-radius: 2px;
  text-align: center;
  display: flex;
  border-top: 1px solid #dcdfe6;
  padding-top: 10px;
}

.device-channel-container {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 8px;
}

.device-status-tag {
  flex-shrink: 0;
  width: 110px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: none !important;
}

.status-icon {
  font-size: 14px;
}

.status-text {
  font-size: 12px;
}

:deep(.el-form-item:last-child) {
  margin-bottom: 0;
}

:deep(.el-tag__content) {
  display: flex;
  align-items: center;
  gap: 4px;
  transition: none !important;
}
</style>
