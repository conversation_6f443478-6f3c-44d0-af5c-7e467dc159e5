<template>
  <div class="testplan-container">
    <el-card class="testplan-card">
      <template #header>
        <div class="panel-header">
          <div class="title-section">
            <h2>{{ testPlan?.manifest.name }}</h2>
          </div>
        </div>
      </template>

      <div class="plan-content">
        <div class="side-menu">
          <SideNav />
        </div>
        <div class="content-area">
          <router-view></router-view>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import SideNav from '@/components/layout/SideNav.vue';
import { testPlanService } from '@/services/testPlanService';

const router = useRouter();
testPlanService.setRouter(router);

const state = testPlanService.getState();
const testPlan = computed(() => state.currentPlan);

const updateTitle = (name?: string) => {
  document.title = name ? `FUZZ - ${name}` : 'FUZZ';
};

onMounted(async () => {
  try {
    const currentPlan = await testPlanService.getCurrentPlan();
    if (currentPlan) {
      updateTitle(currentPlan.manifest.name);
    } else {
      ElMessage.error("No test plan is currently open");
      router.push('/');
    }
  } catch (error) {
    ElMessage.error("Failed to load test plan");
    router.push('/');
  }
});


</script>

<style scoped>
.testplan-container {
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.testplan-card {
  height: 100%;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.el-card__body){
  height: 100%;
  padding: 0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 32px;
}

.title-section h2 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 20px;
}

.plan-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  height: 100%;
}

.side-menu {
  border-right: 1px solid var(--el-border-color-light);
  user-select: none;
}

.content-area {
  display: flex;
  flex: 1;
  overflow-y: auto;
}

.plan-content::-webkit-scrollbar {
  width: 6px;
}

.plan-content::-webkit-scrollbar-thumb {
  background-color: var(--el-border-color-light);
  border-radius: 3px;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.config-section {
  padding: 15px;
  min-height: 200px;
}
</style>
