/**
 * 格式化毫秒时间，最多显示两个单位
 * @param ms 毫秒数
 * @returns 格式化后的时间字符串
 */
export const formatDuration = (ms: number): string => {
  ms = Math.floor(ms);

  if (ms < 1000) {
    return `${ms}ms`;
  } else if (ms < 60000) {
    const seconds = Math.floor(ms / 1000);
    const remainingMs = Math.floor(ms % 1000);
    return `${seconds}s ${remainingMs}ms`;
  } else if (ms < 3600000) {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  } else {
    const hours = Math.floor(ms / 3600000);
    const minutes = Math.floor((ms % 3600000) / 60000);
    return `${hours}h ${minutes}m`;
  }
};

export const formatDateTime = (date?: string | Date | null): string => {
  if (!date) return '-';

  try {
    const dateValue = typeof date === 'string' ? new Date(date) : date;
    const year = dateValue.getFullYear();
    const month = String(dateValue.getMonth() + 1).padStart(2, '0');
    const day = String(dateValue.getDate()).padStart(2, '0');
    const hours = String(dateValue.getHours()).padStart(2, '0');
    const minutes = String(dateValue.getMinutes()).padStart(2, '0');
    const seconds = String(dateValue.getSeconds()).padStart(2, '0');
    return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
  } catch (e) {
    return date.toLocaleString();
  }
};

export const formatDateTimeWithTimezone = (date?: string | Date | null): string => {
  if (!date) return '-';

  try {
    const dateValue = typeof date === 'string' ? new Date(date) : date;
    const year = dateValue.getFullYear();
    const month = String(dateValue.getMonth() + 1).padStart(2, '0');
    const day = String(dateValue.getDate()).padStart(2, '0');
    const hours = String(dateValue.getHours()).padStart(2, '0');
    const minutes = String(dateValue.getMinutes()).padStart(2, '0');
    const seconds = String(dateValue.getSeconds()).padStart(2, '0');
    
    // 获取时区偏移量
    const offset = -dateValue.getTimezoneOffset();
    const offsetHours = Math.floor(offset / 60);
    const timezone = `UTC${offsetHours >= 0 ? '+' : ''}${offsetHours}`;
    
    return `${year}/${month}/${day} ${hours}:${minutes}:${seconds} (${timezone})`;
  } catch (e) {
    return date.toLocaleString();
  }
};

// 格式化剩余秒数为可读时间字符串
export const formatRemainingSeconds = (remainingSeconds?: number): string => {
  if (remainingSeconds === undefined || remainingSeconds === null) return '-';
  
  if (remainingSeconds <= 0) {
    return 'Expired';
  }
  
  const days = Math.floor(remainingSeconds / (60 * 60 * 24));
  const hours = Math.floor((remainingSeconds % (60 * 60 * 24)) / (60 * 60));
  const minutes = Math.floor((remainingSeconds % (60 * 60)) / 60);
  
  if (days > 0) {
    return `${days} days ${hours} hours`;
  } else if (hours > 0) {
    return `${hours} hours ${minutes} minutes`;
  } else {
    return `${minutes} minutes`;
  }
};
