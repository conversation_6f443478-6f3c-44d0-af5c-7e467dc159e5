import { AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { mockTestPlanApi } from './modules/mockTestPlanApi';
import { mockTestPlanHistoryApi } from './modules/mockTestPlanHistoryApi';
import { mockAppApi } from './modules/mockAppApi';
import { mockExplorerApi } from './modules/mockExplorerApi';
import { mockTestSuiteApi } from './modules/mockTestSuiteApi';
import { mockHardwareApi } from './modules/mockHardwareApi';
import { mockSequenceApi } from './modules/mockSequenceApi';
import { mockInteroperationApi } from './modules/mockInteroperationApi';
import { mockCaseApi } from './modules/mockCaseApi';
import { mockLicenseApi } from './modules/mockLicenseApi';

// 控制是否启用Mock
export const USE_MOCK = process.env.NODE_ENV === 'development';

// 模拟网络延迟
const delay = (ms = 100) => new Promise(resolve => setTimeout(resolve, ms));

// 创建一个符合AxiosResponse类型的响应对象
export const mockSuccess = <T>(data: T): Promise<AxiosResponse<T>> => {
  return delay().then(() => {
    // 创建一个符合AxiosResponse要求的对象
    const response: AxiosResponse<T> = {
      data,
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {
        headers: {} // 添加必要的headers属性
      } as InternalAxiosRequestConfig
    };
    return response;
  });
};

// 模拟错误响应
export const mockError = (status = 400, message = 'Error'): Promise<never> => {
  return delay().then(() => {
    const error: any = new Error(message);
    error.response = {
      status,
      data: message
    };
    return Promise.reject(error);
  });
};

// 随机生成一个UUID
export const generateId = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

// 模拟API实现
export const mockApi = {
  // 导出各个模块化的API
  testPlan: mockTestPlanApi,
  testPlanHistory: mockTestPlanHistoryApi,
  app: mockAppApi,
  explorer: mockExplorerApi,
  testSuite: mockTestSuiteApi,
  hardware: mockHardwareApi,
  sequence: mockSequenceApi,
  interoperation: mockInteroperationApi,  // 添加互操作测试API
  license: mockLicenseApi,  // 添加许可证API
  case: {
    ...mockCaseApi,
    getGeneratingProgress: mockCaseApi.getGeneratingProgress
  }
};
