<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="page-container">
    <div class="pagination">
      <el-pagination v-model:current-page="currentPage" v-model:page-size="currentPageSize" :page-sizes="props.pageSizes"
        size="small" :total="props.totalCount" :background="true" layout="sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable no-undef */
import { ref, watch } from 'vue';

interface Props {
  pageNumber: number;
  pageSize: number;
  totalCount: number;
  pageSizes?: number[];
}

interface Emits {
  (e: 'page-change', pageNumber: number, pageSize: number): void;
}

const props = withDefaults(defineProps<Props>(), {
  pageSizes: () => [100, 500, 1000, 2000]
});

const emit = defineEmits<Emits>();

// 创建本地响应式状态
const currentPage = ref(props.pageNumber);
const currentPageSize = ref(props.pageSize);

// 监听 props 变化并同步到本地状态
watch(() => props.pageNumber, (newVal) => {
  currentPage.value = newVal;
}, { immediate: true });

watch(() => props.pageSize, (newVal) => {
  currentPageSize.value = newVal;
}, { immediate: true });

const handleSizeChange = (newSize: number) => {
  emit('page-change', 1, newSize);
};

const handleCurrentChange = (newPage: number) => {
  emit('page-change', newPage, props.pageSize);
};
</script>

<style scoped lang="scss">
.page-container {
  border-top: 1px solid #e4e7ed;
}

.pagination {
  margin: 6px 10px;
}
</style>
