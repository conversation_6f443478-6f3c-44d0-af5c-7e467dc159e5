<template>
  <div class="history-section">
    <div class="history-header">
      <div class="header-title">
        <h3>Recently Opened</h3>
      </div>
      <el-button
        v-if="historyList.length > 0"
        type="text"
        size="small"
        @click="handleClearHistory"
        class="clear-btn"
        title="Clear history"
      >
        <font-awesome-icon icon="trash-can" />
      </el-button>
    </div>

    <!-- 显示加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" />
    </div>

    <el-empty
      v-else-if="!historyList.length"
      description="No recent items"
      :image-size="150"
    >
      <template #image>
        <font-awesome-icon icon="file-circle-exclamation" class="empty-icon" />
      </template>
      <template #description>
        <p class="empty-description">No recent items</p>
        <p class="empty-tip">Recently opened plans will appear here</p>
      </template>
    </el-empty>

    <template v-else>
      <div class="history-list" v-loading="loading">
        <div
          v-for="(item, index) in displayedHistory"
          :key="item.id"
          class="history-item"
          :class="{ 'history-item-alt': index % 2 === 1 }"
          @click="handleOpenPlan(item.filePath)"
        >
          <div class="item-grid">
            <!-- 左上角: 计划名称 -->
            <div class="item-name">
              <el-tag size="small" effect="plain" class="plan-name">{{ item.planName }}</el-tag>
            </div>

            <!-- 右上角: 打开文件夹按钮 -->
            <div class="item-action">
              <el-button
                type="text"
                size="small"
                @click.stop="handleOpenFolder(item.filePath)"
                title="Open in File Explorer"
              >
                <font-awesome-icon icon="folder-open" />
              </el-button>
            </div>

            <!-- 左下角: 文件路径 -->
            <div class="item-path" :title="item.filePath">
              <font-awesome-icon icon="folder" />
              <span class="file-path">{{ item.filePath }}</span>
            </div>

            <!-- 右下角: 最近访问时间 -->
            <div class="access-time">
              <font-awesome-icon icon="clock" />
              {{ formatDateTime(item.lastAccessTime) }}
            </div>
          </div>
        </div>
      </div>

      <el-pagination
        v-if="historyList.length > pageSize"
        layout="prev, pager, next"
        :total="historyList.length"
        :page-size="pageSize"
        :current-page="currentPage"
        @current-change="handlePageChange"
        class="pagination"
        background
        hide-on-single-page
      />
    </template>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue';
import { testPlanService } from '@/services/testPlanService';
import { ElMessageBox } from 'element-plus';
import { explorerApi } from '@/api/explorerApi';
import { testPlanApi } from '@/api/testPlanApi';
import { testPlanHistoryApi } from '@/api/testPlanHistoryApi';
import { formatDateTime } from '@/utils/timeUtils';

export default defineComponent({
  name: 'TestPlanHistory',
  emits: ['open-plan'],
  setup(props, { emit, expose }) {
    const state = testPlanService.getState();
    const pageSize = 20;
    const currentPage = ref(1);
    const loading = ref(true); // 确保初始时为加载状态

    const historyList = computed(() => state.recentPlans);

    const displayedHistory = computed(() => {
      const start = (currentPage.value - 1) * pageSize;
      const end = start + pageSize;
      return historyList.value.slice(start, end);
    });

    const fetchHistory = async () => {
      loading.value = true; // 开始加载时设置loading为true
      try {
        await testPlanService.loadRecentPlans();
        currentPage.value = 1; // 重置页码
      } finally {
        loading.value = false; // 加载完成后设置loading为false
      }
    };

    const handlePageChange = (page: number) => {
      currentPage.value = page;
    };

    const handleClearHistory = () => {
      ElMessageBox.confirm(
        'Are you sure you want to clear all recent items?',
        'Warning',
        {
          confirmButtonText: 'Confirm',
          cancelButtonText: 'Cancel',
          type: 'warning'
        }
      ).then(async () => {
        await testPlanService.clearHistory();
      }).catch(() => {
        // 用户取消操作，不需要做任何处理
      });
    };

    const handleOpenPlan = async (path: string) => {
      try {
        // 先检查文件是否存在
        const response = await testPlanApi.checkFileExists(path);

        if (response.data.exists) {
          // 文件存在，正常打开
          emit('open-plan', path);
        } else {
          // 文件不存在，提示用户
          ElMessageBox.confirm(
            '测试计划文件不存在，是否从历史记录中删除该条目？',
            '文件不存在',
            {
              confirmButtonText: '删除',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).then(async () => {
            // 用户选择删除
            await testPlanHistoryApi.deleteRecord(path);
            // 刷新历史记录
            fetchHistory();
          }).catch(() => {
            // 用户取消，不做任何操作
          });
        }
      } catch (error) {
        console.error('检查文件存在性失败:', error);
      }
    };

    const handleOpenFolder = async (path: string) => {
      try {
        await explorerApi.openExplorer(path);
      } catch (error) {
        console.error('Failed to open folder', error);
      }
    };

    // 组件挂载时获取历史记录
    onMounted(fetchHistory);

    // 暴露刷新方法给父组件
    expose({
      refresh: fetchHistory
    });

    return {
      historyList,
      displayedHistory,
      currentPage,
      pageSize,
      loading, // 返回loading变量
      handleClearHistory,
      handleOpenPlan,
      handleOpenFolder,
      handlePageChange,
      formatDateTime // 添加formatDateTime到返回对象中
    };
  }
});
</script>

<style scoped>
.history-section {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-title h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.header-icon {
  color: var(--el-color-primary);
  font-size: 18px;
}

.clear-btn {
  color: var(--el-text-color-primary);
  padding: 4px;
  font-size: 16px;
}

.empty-icon {
  font-size: 60px;
  color: #DDD;
}

.empty-description {
  margin: 10px 0 0;
  font-size: 16px;
  color: var(--el-text-color-secondary);
}

.empty-tip {
  margin: 5px 0 0;
  font-size: 14px;
  color: var(--el-text-color-placeholder);
}

.history-list {
  flex: 1;
  overflow-y: auto;
}

.history-item {
  padding: 12px 16px;
  margin-bottom: 8px;
  border-radius: 6px;
  background-color: var(--el-bg-color-page);
  border-left: 3px solid var(--el-color-primary-light-5);
  cursor: pointer;
}

.history-item:hover {
  background-color: var(--el-color-primary-light-9);
}

.history-item-alt {
  background-color: var(--el-fill-color-lighter);
  border-left-color: var(--el-color-success-light-5);
}

.history-item-alt:hover {
  background-color: var(--el-color-success-light-9);
}

.item-grid {
  display: grid;
  grid-template-columns: 1fr auto;
  grid-template-rows: auto auto;
  grid-gap: 8px;
  width: 100%;
}

.item-name {
  grid-column: 1;
  grid-row: 1;
}

.item-action {
  margin-right: -10px;
  grid-column: 2;
  grid-row: 1;
  justify-self: end;
}

.item-path {
  grid-column: 1;
  grid-row: 2;
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--el-text-color-secondary);
  font-size: 13px;
  min-width: 0;
}

.access-time {
  grid-column: 2;
  grid-row: 2;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  justify-self: end;
}

.plan-name {
  font-weight: 500;
  padding: 4px 8px;
  max-width: 200px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.file-path {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* 移除不再需要的样式 */
.item-content,
.item-header,
.item-actions {
  display: none;
}

.pagination {
  margin-top: 16px;
  text-align: center;
}

/* 添加加载容器样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  min-height: 200px;
}

</style>
