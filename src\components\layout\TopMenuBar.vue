<template>
  <div class="top-menu-bar">
    <el-menu mode="horizontal" class="menu-container" router :default-active="activeIndex">
      <el-menu-item index="/" class="home-menu-item">
        <el-icon>
          <HomeFilled />
        </el-icon>
      </el-menu-item>
      <el-sub-menu index="file">
        <template #title>File</template>
        <el-menu-item @click="handleOpen" class="menu-item">Open</el-menu-item>
        <el-menu-item @click="handleCreate" class="menu-item">Create</el-menu-item>
        <el-divider />
        <el-sub-menu index="recent" popper-class="recent-submenu">
          <template #title>Recent</template>
          <div v-if="recentPlans.length === 0" class="empty-recent">
            <span>No recent items</span>
          </div>
          <template v-else>
            <el-menu-item v-for="plan in recentPlans" :key="plan.id" @click="handleOpenFromHistory(plan.filePath)"
              class="recent-item">
              <span class="recent-plan-name">{{ plan.planName }}</span>
              <span class="recent-plan-path">{{ plan.filePath }}</span>
            </el-menu-item>
          </template>
        </el-sub-menu>
        <el-divider />
        <el-menu-item @click="handleExit" class="menu-item">Exit</el-menu-item>
      </el-sub-menu>

      <el-menu-item index="/test-suite" class="test-suite-menu-item">
        <span class="test-suite-text">Test Suite</span>
      </el-menu-item>

      <el-menu-item index="/license" class="license-menu-item">
        <span class="license-text">License</span>
      </el-menu-item>

      <el-menu-item index="/about" class="about-menu-item">
        <span class="about-text">About</span>
      </el-menu-item>
    </el-menu>

    <!-- 添加测试计划状态指示器 -->
    <test-plan-status-indicator class="status-indicator" />

    <create-test-plan ref="createDialog" @created="handleTestPlanCreated" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import { appApi } from '@/api'
import { HomeFilled } from '@element-plus/icons-vue'
import CreateTestPlan from '@/components/TestPlan/CreateTestPlan.vue'
import { testPlanService } from '@/services/testPlanService'
import { testPlanApi } from '@/api/testPlanApi'
import { testPlanHistoryApi } from '@/api/testPlanHistoryApi'
import { useLicenseCheck } from '@/utils/useLicenseCheck'
import TestPlanStatusIndicator from './TestPlanStatusIndicator.vue'

const router = useRouter()
const route = useRoute()
const createDialog = ref<InstanceType<typeof CreateTestPlan> | null>(null)
const { checkLicenseWithPrompt } = useLicenseCheck()

// Compute active menu index based on current route
const activeIndex = computed(() => {
  // When on test plan page, don't highlight any menu item
  if (route.path.startsWith('/test-plan')) {
    return ''
  }
  return route.path
})

// Set router
testPlanService.setRouter(router)

// Get service state
const state = testPlanService.getState()
const recentPlans = computed(() => state.recentPlans.slice(0, 5)) // Only show 5 most recent

// Handle open
const handleOpen = async () => {
  const result = await checkLicenseWithPrompt()
  if (result.hasValidLicense) {
    await testPlanService.openFromExplorer()
  }
}

// Handle opening from history
const handleOpenFromHistory = async (path: string) => {
  const result = await checkLicenseWithPrompt()
  if (!result.hasValidLicense) {
    return
  }

  try {
    // First check if file exists
    const response = await testPlanApi.checkFileExists(path);

    if (response.data.exists) {
      // File exists, open normally
      await testPlanService.openFromPath(path);
    } else {
      // File does not exist, prompt user
      ElMessageBox.confirm(
        'Test plan file does not exist. Do you want to remove it from history?',
        'File Not Found',
        {
          confirmButtonText: 'Remove',
          cancelButtonText: 'Cancel',
          type: 'warning'
        }
      ).then(async () => {
        // User chose to remove
        await testPlanHistoryApi.deleteRecord(path);
        // Refresh history
        await testPlanService.loadRecentPlans();
      }).catch(() => {
        // User canceled, do nothing
      });
    }
  } catch (error) {
    console.error('Failed to check file existence:', error);
  }
}

// Handle create
const handleCreate = async () => {
  const result = await checkLicenseWithPrompt()
  if (result.hasValidLicense) {
    createDialog.value?.show()
  }
}

// Handle test plan creation completed
const handleTestPlanCreated = () => {
  // Refresh state is handled by the service, no additional action needed
}

// Handle exit application
const handleExit = () => {
  ElMessageBox.confirm(
    'Are you sure you want to exit the application?',
    'Exit',
    {
      confirmButtonText: 'OK',
      cancelButtonText: 'Cancel',
      type: 'warning'
    }
  ).then(() => {
    // 确认退出时调用后端API
    appApi.exit()
      .then(() => {
        ElMessage.info('Application is shutting down...');
        // 在实际环境中，这里不会被执行到，因为应用已经退出
      })
      .catch((error) => {
        ElMessage.error('Failed to exit the application');
        console.error('Error when exiting application:', error);
      });
  }).catch(() => {
    // 用户取消退出，不执行任何操作
  });
}

onMounted(() => {
  testPlanService.loadRecentPlans()
})
</script>

<style scoped lang="scss">
.top-menu-bar {
  border-bottom: 1px solid var(--el-border-color-light);
  user-select: none;
  display: flex; /* 添加flex布局 */
  align-items: center; /* 垂直居中 */
}

.menu-container {
  height: 40px;
  line-height: 40px;
  display: flex;
  justify-content: flex-start;
  flex-grow: 1; /* 让菜单占据剩余空间 */
}

.status-indicator {
  margin-left: auto; /* 将状态指示器推到右侧 */
}

// 添加自定义样式，减少菜单下拉框与菜单项之间的间距
:deep(.el-menu--horizontal) {
  .el-sub-menu .el-menu--popup {
    margin-top: 0;
  }

  // 二级菜单项左对齐
  .el-menu-item,
  .el-sub-menu__title {
    text-align: left;
    justify-content: flex-start;
  }
}

:deep(.el-menu-item) {
  user-select: none;
}

// 修改关于菜单项样式，确保正确显示
.about-menu-item {
  min-width: unset !important;
  padding: 0 20px !important;
  height: 40px !important;
  line-height: 40px !important;
  overflow: visible !important;
}

.about-text {
  white-space: nowrap !important;
  display: inline-block;
  font-size: 14px;
}

// 保留图标样式，以防将来需要添加其他图标
.el-icon {
  margin-right: 5px;
  vertical-align: middle;
}

.home-menu-item {
  padding: 0 10px;

  .el-icon {
    font-size: 16px;
    margin-right: 0;
    color: var(--el-color-primary);
  }
}

.recent-submenu {
  min-width: 300px;
}

.empty-recent {
  padding: 12px 20px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.recent-item {
  display: flex;
  padding: 8px 16px;
  flex-direction: row;
  gap: 10px;

  .recent-plan-name {
    font-size: 12px;
  }

  .recent-plan-path {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.test-suite-menu-item {
  min-width: unset !important;
  padding: 0 20px !important;
  height: 40px !important;
  line-height: 40px !important;
}

.test-suite-text {
  white-space: nowrap !important;
  display: inline-block;
  font-size: 14px;
}
</style>
