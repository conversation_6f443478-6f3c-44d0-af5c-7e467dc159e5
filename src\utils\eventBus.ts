import { ref } from 'vue'

// 事件类型定义
export type EventType = 'license-updated' | 'license-deleted'

// 事件监听器类型
export type EventListener = (...args: any[]) => void

// 全局事件总线
class EventBus {
  private events: Map<EventType, Set<EventListener>> = new Map()

  // 监听事件
  on(event: EventType, listener: EventListener) {
    if (!this.events.has(event)) {
      this.events.set(event, new Set())
    }
    this.events.get(event)!.add(listener)
  }

  // 移除事件监听
  off(event: EventType, listener: EventListener) {
    const listeners = this.events.get(event)
    if (listeners) {
      listeners.delete(listener)
    }
  }

  // 触发事件
  emit(event: EventType, ...args: any[]) {
    const listeners = this.events.get(event)
    if (listeners) {
      listeners.forEach(listener => listener(...args))
    }
  }

  // 清除所有监听器
  clear() {
    this.events.clear()
  }
}

// 创建全局事件总线实例
export const eventBus = new EventBus()

// License相关事件的辅助函数
export const licenseEvents = {
  // 通知license已更新（上传新license文件）
  notifyLicenseUpdated: () => {
    eventBus.emit('license-updated')
  },
  
  // 通知license已删除
  notifyLicenseDeleted: () => {
    eventBus.emit('license-deleted')
  },
  
  // 监听license更新事件
  onLicenseUpdated: (callback: () => void) => {
    eventBus.on('license-updated', callback)
  },
  
  // 监听license删除事件
  onLicenseDeleted: (callback: () => void) => {
    eventBus.on('license-deleted', callback)
  },
  
  // 移除监听器
  offLicenseUpdated: (callback: () => void) => {
    eventBus.off('license-updated', callback)
  },
  
  offLicenseDeleted: (callback: () => void) => {
    eventBus.off('license-deleted', callback)
  }
}
