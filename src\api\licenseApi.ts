import axios, { AxiosResponse } from 'axios'
import { USE_MOCK } from '../mock/mockApi'
import { mockLicenseApi } from '../mock/modules/mockLicenseApi'

const BASE_URL = '/api/lic'

export interface LicFileInfo {
  id: string
  fileName: string
  creationTime: string
  isExpired: boolean
  expirationTime: string
  remainingSeconds: number
  audience: string
  machineCode: string
  issuer: string
  issueTime: string
}

export interface LicInfo {
  machineCode: string
  hasLicFile: boolean
  licFileInfo: LicFileInfo
  matchMachineCode: boolean
}

export interface DeleteLicenseRequest {
  licenseId: string
}

export const licenseApi = {
  // 获取许可证信息
  getLicenseInfo: (): Promise<AxiosResponse<LicInfo>> => {
    if (USE_MOCK) {
      return mockLicenseApi.getLicenseInfo()
    }
    return axios.get(`${BASE_URL}/info`);
  },

  // 通过服务端文件选择器选择并上传许可证文件
  selectAndUploadLicense: (): Promise<AxiosResponse<LicInfo>> => {
    if (USE_MOCK) {
      return mockLicenseApi.selectAndUploadLicense()
    }
    return axios.get(`${BASE_URL}/select-file`);
  },

  // 删除许可证
  deleteLicense: (request: DeleteLicenseRequest): Promise<AxiosResponse<{ success: boolean; message: string }>> => {
    if (USE_MOCK) {
      return mockLicenseApi.deleteLicense(request.licenseId)
    }
    return axios.post(`${BASE_URL}/delete`, request);
  }
}

export default licenseApi
