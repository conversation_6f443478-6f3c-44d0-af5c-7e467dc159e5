import { reactive, readonly } from 'vue';
import { CaseResult, GroupTreeNode } from '@/api/interoperationApi';

// 缓存数据项接口
interface CacheItem {
  cases: CaseResult[];
  groupTree: GroupTreeNode | null;
  lastUpdateTime: number;
  generationTime: string;
}

// 缓存数据类型
enum CacheType {
  SAVED = 'saved',     // 测试计划中已保存的数据
  GENERATED = 'generated' // 刚生成但未保存的数据
}

// 缓存状态接口
interface TestCaseCacheState {
  // 已保存的用例缓存（来自测试计划）
  savedCache: CacheItem | null;
  // 生成的用例缓存（未保存）
  generatedCache: CacheItem | null;
  // 状态标识
  isLoading: boolean;
}

// 创建内部状态
const state = reactive<TestCaseCacheState>({
  savedCache: null,
  generatedCache: null,
  isLoading: false
});

// 测试用例缓存服务 - 区分保存和生成的数据
class TestCaseCacheService {
  
  /**
   * 获取只读状态
   */
  getState() {
    return readonly(state);
  }

  /**
   * 根据生成时间和类型获取缓存数据
   */
  getCachedData(generationTime: string, type: CacheType): { cases: CaseResult[], groupTree: GroupTreeNode | null } | null {
    const cache = type === CacheType.SAVED ? state.savedCache : state.generatedCache;
    
    if (!cache || cache.generationTime !== generationTime) {
      return null;
    }

    console.log(`从${type}缓存获取数据，时间戳:`, generationTime, '用例数量:', cache.cases.length);
    return {
      cases: [...cache.cases],
      groupTree: cache.groupTree ? { ...cache.groupTree } : null
    };
  }

  /**
   * 存储已保存的用例数据到缓存
   */
  setSavedData(generationTime: string, cases: CaseResult[], groupTree: GroupTreeNode | null): void {
    state.savedCache = {
      cases: [...cases],
      groupTree: groupTree ? { ...groupTree } : null,
      lastUpdateTime: Date.now(),
      generationTime
    };

    console.log('已保存用例数据已缓存，时间戳:', generationTime, '用例数量:', cases.length);
  }

  /**
   * 存储生成的用例数据到缓存
   */
  setGeneratedData(generationTime: string, cases: CaseResult[], groupTree: GroupTreeNode | null): void {
    state.generatedCache = {
      cases: [...cases],
      groupTree: groupTree ? { ...groupTree } : null,
      lastUpdateTime: Date.now(),
      generationTime
    };

    console.log('生成用例数据已缓存，时间戳:', generationTime, '用例数量:', cases.length);
  }

  /**
   * 获取已保存的用例数据
   */
  getSavedData(generationTime: string): { cases: CaseResult[], groupTree: GroupTreeNode | null } | null {
    return this.getCachedData(generationTime, CacheType.SAVED);
  }

  /**
   * 获取生成的用例数据
   */
  getGeneratedData(generationTime: string): { cases: CaseResult[], groupTree: GroupTreeNode | null } | null {
    return this.getCachedData(generationTime, CacheType.GENERATED);
  }

  /**
   * 清除指定类型的缓存
   */
  clearCache(type?: CacheType): void {
    if (type === CacheType.SAVED) {
      state.savedCache = null;
      console.log('已清除已保存用例缓存');
    } else if (type === CacheType.GENERATED) {
      state.generatedCache = null;
      console.log('已清除生成用例缓存');
    } else {
      // 清除所有缓存
      state.savedCache = null;
      state.generatedCache = null;
      console.log('已清除所有缓存');
    }
  }

  /**
   * 将生成的用例转移到已保存缓存（用于保存操作后）
   */
  moveGeneratedToSaved(): void {
    if (state.generatedCache) {
      state.savedCache = {
        ...state.generatedCache,
        lastUpdateTime: Date.now()
      };
      state.generatedCache = null;
      console.log('生成的用例已转移到已保存缓存');
    }
  }

  /**
   * 检查是否有生成的用例
   */
  hasGeneratedData(): boolean {
    return state.generatedCache !== null;
  }

  /**
   * 检查是否有已保存的用例
   */
  hasSavedData(): boolean {
    return state.savedCache !== null;
  }

  /**
   * 根据生成时间获取对应的缓存数据（已保存的）
   */
  getDataByGenerationTime(generationTime: string): { cases: CaseResult[], groupTree: GroupTreeNode | null, isGenerated: boolean } | null {
    // 检查已保存的用例缓存
    if (state.savedCache && state.savedCache.generationTime === generationTime) {
      return {
        cases: [...state.savedCache.cases],
        groupTree: state.savedCache.groupTree ? { ...state.savedCache.groupTree } : null,
        isGenerated: false
      };
    }
    
    return null;
  }

  /**
   * 设置加载状态
   */
  setLoadingState(isLoading: boolean): void {
    state.isLoading = isLoading;
  }

  /**
   * 获取加载状态
   */
  isLoadingState(): boolean {
    return state.isLoading;
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): {
    hasSavedData: boolean;
    hasGeneratedData: boolean;
    savedCacheInfo: { generationTime: string; caseCount: number; lastUpdateTime: number } | null;
    generatedCacheInfo: { generationTime: string; caseCount: number; lastUpdateTime: number } | null;
    totalCaseCount: number;
  } {
    const savedInfo = state.savedCache ? {
      generationTime: state.savedCache.generationTime,
      caseCount: state.savedCache.cases.length,
      lastUpdateTime: state.savedCache.lastUpdateTime
    } : null;

    const generatedInfo = state.generatedCache ? {
      generationTime: state.generatedCache.generationTime,
      caseCount: state.generatedCache.cases.length,
      lastUpdateTime: state.generatedCache.lastUpdateTime
    } : null;

    return {
      hasSavedData: state.savedCache !== null,
      hasGeneratedData: state.generatedCache !== null,
      savedCacheInfo: savedInfo,
      generatedCacheInfo: generatedInfo,
      totalCaseCount: (savedInfo?.caseCount || 0) + (generatedInfo?.caseCount || 0)
    };
  }
}

// 创建单例实例
const testCaseCacheService = new TestCaseCacheService();

export { testCaseCacheService, type TestCaseCacheState, type CacheItem, CacheType };
