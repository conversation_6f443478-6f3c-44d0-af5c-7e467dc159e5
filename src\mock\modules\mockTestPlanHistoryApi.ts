import { TestPlanHistory } from '@/api/testPlanHistoryApi';
import { mockSuccess } from '../mockApi';
import { testPlanHistoryItems } from '../mockData';
import { AxiosResponse } from 'axios';

// 测试计划历史 API 模拟实现
export const mockTestPlanHistoryApi = {
  // 获取历史记录
  get: (): Promise<AxiosResponse<TestPlanHistory[]>> => {
    // 过滤掉已删除的记录，并按最后访问时间降序排序
    const sortedHistory = testPlanHistoryItems
      .filter(item => !item.isDeleted)
      .sort((a, b) => {
        // 按最后访问时间降序排序（新的在前）
        return new Date(b.lastAccessTime).getTime() - new Date(a.lastAccessTime).getTime();
      });

    return mockSuccess(sortedHistory);
  },

  // 清空历史记录
  clear: (): Promise<AxiosResponse<void>> => {
    // 标记所有记录为已删除
    testPlanHistoryItems.forEach(item => {
      item.isDeleted = true;
    });

    return mockSuccess(undefined);
  },

  // 删除单个历史记录
  deleteRecord: (filePath: string): Promise<AxiosResponse<void>> => {
    // 查找匹配的记录
    const record = testPlanHistoryItems.find(item => item.filePath === filePath && !item.isDeleted);

    // 如果找到记录，标记为已删除
    if (record) {
      record.isDeleted = true;
    }

    return mockSuccess(undefined);
  }
};
