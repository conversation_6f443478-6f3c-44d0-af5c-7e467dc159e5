import { mockSuccess, mockError } from '../mockApi';

// 添加模拟的测试套件数据
const mockTestSuites = [
  {
    name: 'CAN-Bus Test Suite',
    version: '1.0',
    packages: [
      {
        name: 'CAN Frame sequences (ISO 11898-1)',
        sequences: [
          {
            type: 'can',
            description: { name: 'can-frames' },
            preamble: { onConnect: true, value: 'can-preamble-sequence' }
          }
        ]
      }
    ]
  },
  {
    name: 'CAN-FD Test Suite',
    version: '1.0',
    packages: [
      {
        name: 'CAN Unified Diagnostic Services (ISO 14229-1)',
        sequences: [
          {
            type: 'uds',
            description: { name: 'can-uds.diagnostic-session-control' },
            preamble: { onConnect: true, value: 'uds-preamble-sequence' }
          }
        ]
      }
    ]
  }
];

// 添加模拟的XML数据
const mockSequenceXml: Record<string, string> = {
  'CAN Frame sequences (ISO 11898-1)': 
`<sequences setting="sequence">
  <description name="CAN Frame examples">Raw CAN Frame test sequences.</description>
  <sequence type="can">
    <description name="can-frames">CAN frame</description>
    <preamble on-connect="true">can-preamble-sequence</preamble>
  </sequence>
</sequences>`,
  'CAN-FD Test Suite': 
`<sequences setting="sequence">
  <description name="CAN Unified Diagnostic Services (ISO 14229-1)">UDS test sequences.</description>
  <sequence type="uds">
    <description name="can-uds.diagnostic-session-control">UDS session control</description>
    <preamble on-connect="true">uds-preamble-sequence</preamble>
  </sequence>
</sequences>`
};

// 测试套件 API 模拟实现
export const mockTestSuiteApi = {
  getBuiltIn: () => {
    return mockSuccess(mockTestSuites);
  },
  
  getXml: (suiteName: string, packageName: string, customName?: string) => {
    const suite = mockTestSuites.find(s => s.name === suiteName);
    if (!suite) {
      return mockError(400, `Test suite '${suiteName}' not found`);
    }

    // 如果是自定义包，返回自定义XML（这里简化处理）
    if (customName) {
      return mockSuccess(`<sequences setting="sequence">
  <description name="${customName}">Custom sequence package.</description>
  <sequence type="custom">
    <description name="custom-sequence">Custom sequence</description>
    <preamble on-connect="true">custom-preamble-sequence</preamble>
  </sequence>
</sequences>`);
    }

    const pkg = suite.packages.find(p => p.name === packageName);
    if (!pkg) {
      return mockError(400, `Package '${packageName}' not found in test suite '${suiteName}'`);
    }

    return mockSuccess(mockSequenceXml[suiteName]);
  }
};
