import axios, { AxiosResponse } from 'axios'
import { USE_MOCK, mockApi } from '@/mock/mockApi'

// 定义设备通道数据结构
export interface DeviceChannel {
  name: string;
  communicationType: string;
  isConnected: boolean;
}

// 定义CAN配置数据结构
export interface CanConfig {
  deviceChannelName: string;
  dataBitrate: number;
}

// 定义CANFD配置数据结构
export interface CanFdConfig {
  deviceChannelName: string;
  arbitrationBitrate: number;
  dataBitrate: number;
}

// 定义测试计划配置数据结构
export interface TestPlanConfig {
  communicationType: string;
  canConfig?: CanConfig;
  canFdConfig?: CanFdConfig;
}

// 定义硬件配置数据结构
export interface HardwareConfig {
  deviceChannels: DeviceChannel[];
  testPlanConfig: TestPlanConfig;
}

// 定义配置选项数据结构
export interface HardwareOptions {
  canBaudRates: number[];
  canFdArbitrationBaudRates: number[];
  canFdDataBaudRates: number[];
}

const BASE_URL = '/api/hardwareConfig'

export const hardwareApi = {
  // 获取硬件配置
  getHardwareConfig: (): Promise<AxiosResponse<HardwareConfig>> => {
    if (USE_MOCK) {
      return mockApi.hardware.getHardwareConfig();
    }
    return axios.get(BASE_URL);
  },

  // 更新硬件配置
  updateHardwareConfig: (config: TestPlanConfig): Promise<AxiosResponse<HardwareConfig>> => {
    if (USE_MOCK) {
      return mockApi.hardware.updateHardwareConfig(config);
    }
    return axios.post(`${BASE_URL}/update`, config);
  }
}

export default hardwareApi
