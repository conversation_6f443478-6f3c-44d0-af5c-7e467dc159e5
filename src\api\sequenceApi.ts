import axios from 'axios'
import { USE_MOCK, mockApi } from '@/mock/mockApi'

// 定义序列配置数据结构
export interface SequenceConfigData {
  testSuiteName: string;
  sequencePackageName: string;
  sequencePackageXml?: string;
  isBuiltIn: boolean;
  customName?: string;
  basePackageName?: string;
  lastModified?: string;
  creationTime?: string;
}

export enum CommunicationType {
  Can = 0,
  Canfd = 1
}

const BASE_URL = '/api/sequenceConfig'

export const sequenceApi = {
  // 获取序列配置
  getSequenceConfig: () => {
    if (USE_MOCK) {
      return mockApi.sequence.getSequenceConfig();
    }
    return axios.get<SequenceConfigData>(`${BASE_URL}`);
  },

  // 更新序列配置
  updateSequenceConfig: (config: SequenceConfigData) => {
    if (USE_MOCK) {
      return mockApi.sequence.updateSequenceConfig(config);
    }
    return axios.post<SequenceConfigData>(`${BASE_URL}`, config);
  },

  // 删除自定义序列包
  deleteCustomSequencePackage: (testSuiteName: string, customName: string) => {
    if (USE_MOCK) {
      return mockApi.sequence.deleteCustomSequencePackage(testSuiteName, customName);
    }
    return axios.delete(`${BASE_URL}`, {
      params: { testSuiteName, customName }
    });
  },

  // 获取自定义包列表
  getCustomPackages: (testSuiteName: string) => {
    if (USE_MOCK) {
      return mockApi.sequence.getCustomPackages(testSuiteName);
    }
    return axios.get(`${BASE_URL}/custom-packages`, {
      params: { testSuiteName }
    });
  }
}

export default sequenceApi
