import axios, { AxiosResponse } from 'axios'
import { USE_MOCK, mockApi } from '@/mock/mockApi'

export interface SequencePackage {
  name: string;
  sequences: any[];
  isBuiltIn?: boolean;
  customName?: string;
  testSuiteName?: string;
  lastModified?: string;
  creationTime?: string;
}

export interface TestSuite {
  name: string;
  version: string;
  packages: SequencePackage[];
}

const BASE_URL = '/api/testsuite'

export const testSuiteApi = {
  getBuiltIn: (): Promise<AxiosResponse<TestSuite[]>> => {
    if (USE_MOCK) {
      return mockApi.testSuite.getBuiltIn();
    }
    return axios.get(`${BASE_URL}/builtin`);
  },

  getBuiltInXml: (suiteName: string, packageName: string): Promise<AxiosResponse<string>> => {
    if (USE_MOCK) {
      return mockApi.testSuite.getXml(suiteName, packageName);
    }
    return axios.get(`${BASE_URL}/builtin-xml`, { params: { suiteName, packageName } });
  },

  getXml: (suiteName: string, packageName: string, customName?: string): Promise<AxiosResponse<string>> => {
    if (USE_MOCK) {
      return mockApi.testSuite.getXml(suiteName, packageName, customName);
    }
    const params: any = { suiteName, packageName };
    if (customName) {
      params.customName = customName;
    }
    return axios.get(`${BASE_URL}/xml`, { params });
  }
}

export default testSuiteApi
