<template>
  <div class="side-nav-wrapper">
    <el-menu :collapse="collapsed" :collapse-transition="false" class="side-nav"
      :class="{ 'side-nav-collapsed': collapsed }" router :default-active="route.path">
      <el-menu-item index="/test-plan">
        <el-icon>
          <Document />
        </el-icon>
        <span>Basic Setting</span>
      </el-menu-item>

      <el-menu-item index="/test-plan/hardware">
        <font-awesome-icon icon="sliders" />
        <span>Hardware Setting</span>
      </el-menu-item>

      <el-menu-item index="/test-plan/case-setting">
        <el-icon>
          <Setting />
        </el-icon>
        <span>Case Setting</span>
      </el-menu-item>

      <el-menu-item index="/test-plan/sequence-setting">
        <font-awesome-icon icon="laptop-code" />
        <span>Sequence Setting</span>
      </el-menu-item>

      <el-menu-item index="/test-plan/interoperation">
        <el-icon>
          <Connection />
        </el-icon>
        <span>Interoperation</span>
      </el-menu-item>

      <el-menu-item index="/test-plan/test-cases">
        <el-icon>
          <List />
        </el-icon>
        <span>Test Cases</span>
      </el-menu-item>

      <el-menu-item index="/test-plan/test-run">
        <el-icon>
          <VideoPlay />
        </el-icon>
        <span>Test Run</span>
      </el-menu-item>

      <el-menu-item index="/test-plan/test-results">
        <el-icon>
          <DataAnalysis />
        </el-icon>
        <span>Results</span>
      </el-menu-item>
    </el-menu>
    <div class="sidebar-toggle" @click="toggleSidebar">
      <el-icon :size="16">
        <component :is="collapsed ? 'ArrowRight' : 'ArrowLeft'" />
      </el-icon>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Document, List, DataAnalysis, Connection, Monitor, VideoPlay, Setting } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router'

const collapsed = ref(false)
const route = useRoute()

const toggleSidebar = () => {
  collapsed.value = !collapsed.value
}
</script>

<style scoped>
.side-nav-wrapper {
  position: relative;
  height: 100%;
}

.side-nav {
  height: 100%;
  border-right: none;
  transition: width 0.3s;
  width: 200px;
  border-right: 1px solid var(--el-border-color-light);
}

.side-nav-collapsed {
  width: 64px;
}

.sidebar-toggle {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 12px;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 100;
  color: var(--el-text-color-secondary);
  transition: background-color 0.3s;

  &:hover {
    background-color: #f1f1f1;
    color: #777;
  }

  .el-icon {
    font-size: 12px;
  }
}

.el-menu-item .svg-inline--fa {
  margin-right: 10px;
  margin-left: 5px;
}
</style>
