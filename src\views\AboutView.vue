<template>
  <div class="about-container">
    <el-card class="about-card">
      <div class="about-content">
        <div>
          <h2>Fuzz</h2>
          <p class="description">Fuzz is a professional fuzzing tool mainly used for automotive communication
            protocol testing.</p>
        </div>

        <div>
          <h2>Features</h2>
          <ul class="feature-list">
            <li><el-icon class="feature-icon">
                <Check />
              </el-icon>Support for CAN/CANFD protocol testing</li>
            <li><el-icon class="feature-icon">
                <Check />
              </el-icon>Support for ISO 11898, ISO 14229 and ISO 15765 related testing</li>
            <li><el-icon class="feature-icon">
                <Check />
              </el-icon>Interoperation test to detect services supported by the device</li>
            <li><el-icon class="feature-icon">
                <Check />
              </el-icon>Automatic generation and execution of targeted test cases</li>
            <li><el-icon class="feature-icon">
                <Check />
              </el-icon>Local storage of test results and logs</li>
          </ul>
        </div>

        <!-- 应用信息部分 -->
        <div v-if="appInfo">
          <h2>Application Info</h2>
          <div class="info-list">
            <div class="info-item">
              <span class="label">Data Folder:</span>
              <span class="folder-path">{{ appInfo.dataFolder }}</span>
              <el-button 
                type="text" 
                size="small" 
                @click="openFolder(appInfo.dataFolder)"
                title="Open in File Explorer"
                class="folder-btn"
              >
                <font-awesome-icon icon="folder-open" />
              </el-button>
            </div>
            <div class="info-item">
              <span class="label">Log Folder:</span>
              <span class="folder-path">{{ appInfo.logFolder }}</span>
              <el-button 
                type="text" 
                size="small" 
                @click="openFolder(appInfo.logFolder)"
                title="Open in File Explorer"
                class="folder-btn"
              >
                <font-awesome-icon icon="folder-open" />
              </el-button>
            </div>
          </div>
        </div>

        <div class="copyright-info">
          <p>Copyright © 2025, ALPS System Integration(Dalian) Co., Ltd.</p>
          <p>All rights reserved.</p>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { Check } from '@element-plus/icons-vue';
import { appApi } from '@/api/appApi';
import { explorerApi } from '@/api/explorerApi';

// 用于存储应用信息
const appInfo = ref(null);

// 获取应用信息
const fetchAppInfo = async () => {
  try {
    const response = await appApi.getAppInfo();
    appInfo.value = response.data;
  } catch (error) {
    console.error('Failed to fetch app info:', error);
  }
};

// 在文件浏览器中打开文件夹
const openFolder = async (path) => {
  try {
    await explorerApi.openExplorer(path);
  } catch (error) {
    console.error('Failed to open folder:', error);
  }
};

// 组件挂载时获取应用信息
onMounted(fetchAppInfo);
</script>

<style scoped>
.about-container {
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.about-card {
  height: 100%;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 32px;
}

.title-section h2 {
  margin: 0;
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 20px;
  border-bottom: none;
}

.about-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  line-height: 1.6;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.about-content::-webkit-scrollbar {
  width: 6px;
}

.about-content::-webkit-scrollbar-thumb {
  background-color: var(--el-border-color-light);
  border-radius: 3px;
}

h2 {
  font-size: 18px;
  color: var(--el-color-primary);
  border-bottom: 1px solid var(--el-border-color-light);
  padding-bottom: 8px;
  margin-bottom: 16px;
  margin-top: 0;
}

.description {
  font-size: 14px;
  color: var(--el-text-color-primary);
  margin-left: 8px;
}

.feature-list {
  padding-left: 0;
  list-style-type: none;
}

.feature-list li {
  padding: 6px 0;
  font-size: 14px;
  display: flex;
  align-items: center;
  color: var(--el-text-color-primary);
}

.feature-icon {
  color: var(--el-color-success);
  margin-right: 10px;
}

.info-list {
  padding-left: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 14px;
}

.label {
  min-width: 100px;
  color: var(--el-text-color-primary);
}

.folder-path {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--el-text-color-secondary);
  margin-right: 10px;
}

.folder-btn {
  padding: 2px;
}

.copyright-info {
  margin-top: auto;
  padding-top: 20px;
  margin-bottom: 0;
  color: var(--el-text-color-secondary);
  font-size: 12px;
  text-align: center;
  border-top: 1px solid var(--el-border-color-lighter);
}

copyright-info p {
  margin: 5px 0;
}
</style>
