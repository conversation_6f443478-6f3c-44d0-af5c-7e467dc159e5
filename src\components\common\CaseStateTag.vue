<template>
  <el-tag :type="tagType" size="small" style="min-width: 60px;">
    {{ stateName }}
  </el-tag>
</template>

<script setup lang="ts">
import { computed, defineProps } from 'vue';
import { ExecutionState } from '@/api/appApi';

const props = defineProps<{
  state: string;
}>();

const tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {
  switch (props.state) {
    case ExecutionState.Success:
      return 'success';
    case ExecutionState.Running:
      return 'warning';
    case ExecutionState.Failure:
      return 'danger';
    case ExecutionState.Pending:
    default:
      return 'info';
  }
});

const getCaseStateName = (state: string): 'Not Run' | 'Running' | 'Passed' | 'Failed' | 'Unknown' => {
  switch (state) {
    case ExecutionState.Running:
      return 'Running';
    case ExecutionState.Pending:
      return 'Not Run';
    case ExecutionState.Success:
      return 'Passed';
    case ExecutionState.Failure:
      return 'Failed';
    default:
      return 'Unknown';
  }
};

const stateName = computed(() => {
  return getCaseStateName(props.state);
});
</script>
