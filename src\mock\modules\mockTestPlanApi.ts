import { TestPlan } from '@/api/testPlanApi';
import { mockSuccess, mockError, generateId } from '../mockApi';
import { testPlans, testPlanHistoryItems } from '../mockData';
import { AxiosResponse } from 'axios';

// 添加当前测试计划的状态管理
let currentTestPlan: TestPlan | null = null;

// 测试计划 API 模拟实现
export const mockTestPlanApi = {
  // 在文件浏览器中打开测试计划
  openInExplorer: (): Promise<AxiosResponse<TestPlan>> => {
    // 随机选择一个测试计划
    const randomIndex = Math.floor(Math.random() * testPlans.length);
    const selectedPlan = testPlans[randomIndex];
    currentTestPlan = selectedPlan; // 设置当前测试计划

    // 记录历史记录
    updateTestPlanHistory(selectedPlan.path, selectedPlan.name);
    return mockSuccess(selectedPlan);
  },

  // 从指定路径打开测试计划
  open: (path: string): Promise<AxiosResponse<TestPlan>> => {
    // 检查路径是否存在于历史记录中
    const historyItem = testPlanHistoryItems.find(item => item.filePath === path);
    let plan: TestPlan;

    // 如果没找到匹配的历史记录，返回第一个测试计划
    if (!historyItem) {
      plan = testPlans[0];
      // 添加新的历史记录
      updateTestPlanHistory(path, plan.name);
    } else {
      // 查找匹配的测试计划
      const matchingPlan = testPlans.find(p => p.manifest.name === historyItem.planName);
      plan = matchingPlan || testPlans[0];
      // 更新访问时间
      updateTestPlanHistory(path, matchingPlan?.manifest.name || "");
    }

    currentTestPlan = plan; // 设置当前测试计划
    return mockSuccess(plan);
  },

  // 创建测试计划
  create: (data: any): Promise<AxiosResponse<TestPlan>> => {
    // 创建新的测试计划
    const now = new Date().toISOString();

    // 创建文件路径
    const filePath = `D:\\TestPlans\\${data.name.replace(/\s+/g, '_').toLowerCase()}.fzp`;

    const newPlan: TestPlan = {
      path: filePath,
      manifest: {
        name: data.name,
        description: data.description,
        created: now,
        modified: now
      },
      config: {
        targetDevice: "Default",
        protocol: "CAN",
        testCases: []
      }
    };

    // 保存到内存中
    testPlans.push(newPlan);

    // 添加到历史记录
    updateTestPlanHistory(filePath, data.name);

    currentTestPlan = newPlan;
    // 返回创建结果
    return mockSuccess(newPlan);
  },

  // 关闭测试计划
  close: (): Promise<AxiosResponse<void>> => {
    currentTestPlan = null; // 清除当前测试计划
    console.log("模拟关闭测试计划");
    return mockSuccess(undefined);
  },

  // 获取当前测试计划
  getCurrentPlan: (): Promise<AxiosResponse<TestPlan>> => {
    if (!currentTestPlan) {
      return mockError(404, "No test plan is currently open");
    }
    return mockSuccess(currentTestPlan);
  },

  // 更新测试计划基本信息
  updateBasicInfo: (description: string): Promise<AxiosResponse<TestPlan>> => {
    if (!currentTestPlan) {
      return mockError(404, "No test plan is currently open");
    }

    // 更新描述和修改时间
    currentTestPlan.manifest.description = description;
    currentTestPlan.manifest.modified = new Date().toISOString();

    // 如果有对应的历史记录，也更新历史记录的修改时间
    const historyItem = testPlanHistoryItems.find(item => item.filePath === currentTestPlan?.path);
    if (historyItem) {
      historyItem.lastModified = new Date().toISOString();
    }

    return mockSuccess(currentTestPlan);
  },

  // 检查文件是否存在
  checkFileExists: (path: string): Promise<AxiosResponse<{exists: boolean}>> => {
    // 模拟实现：随机返回文件存在或不存在
    // 在实际应用中，可以根据需要调整逻辑
    // 这里我们假设90%的文件存在，10%的文件不存在
    const exists = Math.random() > 0.1;

    // 也可以根据特定路径模拟不同的结果
    // 例如：特定路径总是返回不存在
    // if (path.includes('nonexistent')) {
    //   exists = false;
    // }

    return mockSuccess({ exists });
  }
};

// 辅助函数：更新测试计划历史记录
function updateTestPlanHistory(filePath: string, planName: string): void {
  const now = new Date().toISOString();
  const existingItem = testPlanHistoryItems.find(item => item.filePath === filePath);

  if (existingItem) {
    // 更新现有记录
    existingItem.lastAccessTime = now;
    existingItem.lastModified = now;
    existingItem.isDeleted = false;
  } else {
    // 添加新记录
    testPlanHistoryItems.push({
      id: generateId(),
      filePath,
      planName,
      lastAccessTime: now,
      lastModified: now,
      isDeleted: false
    });
  }

  // 重新排序历史记录，按最后访问时间降序排序
  testPlanHistoryItems.sort((a, b) => {
    if (a.isDeleted && !b.isDeleted) return 1;  // 已删除的放后面
    if (!a.isDeleted && b.isDeleted) return -1; // 未删除的放前面
    // 都是未删除或都是已删除的情况下，按最后访问时间降序排序
    return new Date(b.lastAccessTime).getTime() - new Date(a.lastAccessTime).getTime();
  });
}
