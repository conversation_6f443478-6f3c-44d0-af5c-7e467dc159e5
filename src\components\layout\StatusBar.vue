<template>
  <div class="status-bar">
    <div class="status-item">
      <div class="status-indicator" @click="navigateToLicense">
        <span :class="statusDotClass"></span>
        <span class="status-text">{{ statusText }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { licenseApi, type LicInfo } from '@/api/licenseApi'
import { formatDateTimeWithTimezone, formatRemainingSeconds } from '@/utils/timeUtils'
import { licenseEvents } from '@/utils/eventBus'

const router = useRouter()

// 状态数据
const licenseInfo = ref<LicInfo | null>(null)
const isLoading = ref(false)
let refreshTimer: number | null = null

// 计算状态点的CSS类
const statusDotClass = computed(() => {
  if (isLoading.value) {
    return 'status-dot loading'
  }
  
  if (!licenseInfo.value) {
    return 'status-dot gray'
  }
  
  if (!licenseInfo.value.hasLicFile) {
    return 'status-dot gray'
  }
  
  if (licenseInfo.value.licFileInfo.isExpired) {
    return 'status-dot orange'
  }
  
  if (!licenseInfo.value.matchMachineCode) {
    return 'status-dot orange'
  }
  
  return 'status-dot green'
})

// 计算状态文本
const statusText = computed(() => {
  if (isLoading.value) {
    return 'Loading license info...'
  }
  
  if (!licenseInfo.value) {
    return 'License: Unable to load'
  }
  
  if (!licenseInfo.value.hasLicFile) {
    return 'License: No license file'
  }
  
  const { licFileInfo } = licenseInfo.value
  
  if (licFileInfo.isExpired) {
    return `License: Expired on ${formatDateTimeWithTimezone(new Date(licFileInfo.expirationTime))}`
  }
  
  if (!licenseInfo.value.matchMachineCode) {
    return 'License: Machine code mismatch'
  }
  
  const remainingTime = formatRemainingSeconds(licFileInfo.remainingSeconds)
  return `License: ${remainingTime} remaining`
})

// 获取license信息
const fetchLicenseInfo = async () => {
  try {
    isLoading.value = true
    const response = await licenseApi.getLicenseInfo()
    licenseInfo.value = response.data
  } catch (error) {
    console.error('Failed to fetch license info:', error)
    licenseInfo.value = null
  } finally {
    isLoading.value = false
  }
}

// 设置定期刷新
const startRefreshTimer = () => {
  // 每30秒刷新一次license状态
  refreshTimer = window.setInterval(() => {
    fetchLicenseInfo()
  }, 30000)
}

// 清除定时器
const clearRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 处理license更新事件
const handleLicenseUpdate = () => {
  fetchLicenseInfo()
}

// 导航到license页面
const navigateToLicense = () => {
  router.push('/license')
}

// 组件挂载时获取初始数据并启动定时器
onMounted(() => {
  fetchLicenseInfo()
  startRefreshTimer()
  
  // 监听license相关事件
  licenseEvents.onLicenseUpdated(handleLicenseUpdate)
  licenseEvents.onLicenseDeleted(handleLicenseUpdate)
})

// 组件销毁时清除定时器和事件监听
onUnmounted(() => {
  clearRefreshTimer()
  
  // 移除事件监听
  licenseEvents.offLicenseUpdated(handleLicenseUpdate)
  licenseEvents.offLicenseDeleted(handleLicenseUpdate)
})
</script>

<style scoped lang="scss">
.status-bar {
  height: 30px;
  background-color: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color-light);
  display: flex;
  align-items: center;
  padding: 0 16px;
  font-size: 12px;
  color: var(--el-text-color-regular);
  user-select: none;
}

.status-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  
  &:hover {
    opacity: 0.9;
  }
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  
  &.gray {
    background-color: var(--el-text-color-secondary);
  }
  
  &.green {
    background-color: var(--el-color-success);
  }
  
  &.orange {
    background-color: var(--el-color-warning);
  }
  
  &.loading {
    background-color: var(--el-color-primary);
    animation: pulse 1.5s ease-in-out infinite;
  }
}

.status-text {
  max-width: 500px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
