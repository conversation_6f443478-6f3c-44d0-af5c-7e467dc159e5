/**
 * License 提醒本地存储管理器
 * 管理已显示的 license 到期提醒记录
 */

import { licenseEvents } from './eventBus'

// 存储键名
const REMINDER_STORAGE_KEY = 'license_reminder_shown'

// 提醒阈值天数
export const REMINDER_THRESHOLDS = [30, 7, 3, 2, 1] as const

export type ReminderThreshold = typeof REMINDER_THRESHOLDS[number]

interface ReminderRecord {
  licenseId: string
  shownThresholds: ReminderThreshold[]
  lastModified: number
}

/**
 * License 提醒存储管理器
 */
export class LicenseReminderStorage {
  private static isInitialized = false

  /**
   * 初始化事件监听器
   * 在应用启动时调用一次即可
   */
  static initialize(): void {
    if (this.isInitialized) {
      return
    }

    // 监听 license 更新和删除事件
    licenseEvents.onLicenseUpdated(() => {
      // 当 license 更新时，清空所有提醒记录
      // 因为新的 license 可能有不同的到期时间
      this.clearAllReminderRecords()
    })

    licenseEvents.onLicenseDeleted(() => {
      // 当 license 删除时，清空所有提醒记录
      this.clearAllReminderRecords()
    })

    this.isInitialized = true
  }

  /**
   * 检查是否已经显示过指定阈值的提醒
   */
  static hasShownReminder(licenseId: string, threshold: ReminderThreshold): boolean {
    try {
      const record = this.getRecord(licenseId)
      return record ? record.shownThresholds.includes(threshold) : false
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('Failed to check reminder record:', error)
      return false
    }
  }

  /**
   * 标记指定阈值的提醒已显示
   */
  static markReminderShown(licenseId: string, threshold: ReminderThreshold): void {
    try {
      let record = this.getRecord(licenseId)
      
      if (!record) {
        record = {
          licenseId,
          shownThresholds: [],
          lastModified: Date.now()
        }
      }

      if (!record.shownThresholds.includes(threshold)) {
        record.shownThresholds.push(threshold)
        record.lastModified = Date.now()
        this.saveRecord(record)
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('Failed to mark reminder as shown:', error)
    }
  }

  /**
   * 清空指定 license 的所有提醒记录
   */
  static clearReminderRecord(licenseId: string): void {
    try {
      const allRecords = this.getAllRecords()
      const filteredRecords = allRecords.filter(record => record.licenseId !== licenseId)
      localStorage.setItem(REMINDER_STORAGE_KEY, JSON.stringify(filteredRecords))
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('Failed to clear reminder record:', error)
    }
  }

  /**
   * 清空所有提醒记录
   */
  static clearAllReminderRecords(): void {
    try {
      localStorage.removeItem(REMINDER_STORAGE_KEY)
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('Failed to clear all reminder records:', error)
    }
  }

  /**
   * 获取指定 license 的提醒记录
   */
  private static getRecord(licenseId: string): ReminderRecord | null {
    const allRecords = this.getAllRecords()
    return allRecords.find(record => record.licenseId === licenseId) || null
  }

  /**
   * 保存提醒记录
   */
  private static saveRecord(record: ReminderRecord): void {
    const allRecords = this.getAllRecords()
    const existingIndex = allRecords.findIndex(r => r.licenseId === record.licenseId)
    
    if (existingIndex >= 0) {
      allRecords[existingIndex] = record
    } else {
      allRecords.push(record)
    }
    
    localStorage.setItem(REMINDER_STORAGE_KEY, JSON.stringify(allRecords))
  }

  /**
   * 获取所有提醒记录
   */
  private static getAllRecords(): ReminderRecord[] {
    try {
      const stored = localStorage.getItem(REMINDER_STORAGE_KEY)
      return stored ? JSON.parse(stored) : []
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('Failed to parse reminder records from localStorage:', error)
      return []
    }
  }

  /**
   * 获取需要提醒的阈值
   * @param remainingDays 剩余天数
   * @param licenseId license ID
   * @returns 需要提醒的阈值，如果不需要提醒则返回 null
   */
  static getRequiredReminder(remainingDays: number, licenseId: string): ReminderThreshold | null {
    // 找到最接近且小于等于剩余天数的阈值
    let targetThreshold: ReminderThreshold | null = null
    
    for (const threshold of REMINDER_THRESHOLDS) {
      if (remainingDays <= threshold) {
        // 如果当前阈值更接近剩余天数，则更新目标阈值
        if (targetThreshold === null || threshold < targetThreshold) {
          targetThreshold = threshold
        }
      }
    }

    if (!targetThreshold) {
      return null
    }

    // 检查是否已经显示过这个阈值的提醒
    if (this.hasShownReminder(licenseId, targetThreshold)) {
      return null
    }

    return targetThreshold
  }
}
