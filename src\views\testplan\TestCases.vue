<template>
  <div class="test-cases-container">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <!-- 展开/收起全部按钮 -->
        <el-button @click="expandAll" type="primary" size="small" class="expand-button" :disabled="!hasTreeData">
          <font-awesome-icon icon="up-right-and-down-left-from-center" /><span class="button-text">Expand All</span>
        </el-button>
        <el-button @click="collapseAll" type="primary" size="small" class="collapse-button" :disabled="!hasTreeData">
          <font-awesome-icon icon="down-left-and-up-right-to-center" /><span class="button-text">Collapse All</span>
        </el-button>
      </div>
      <div class="action-buttons">
        <el-button type="success" @click="openGenerateDialog" size="small">
          Generate Cases
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 - 显示 Case Groups 和用例列表 -->
    <div class="content-area">
      <!-- 左侧：分组树 -->
      <div class="dialog-left">
        <!-- 分组树上方的总览信息 -->
        <div class="group-info">
          <span>Total {{ totalCaseCount }} cases</span>
        </div>
        <div class="group-border">
          <div class="tree-header">
            <h4>Case Groups</h4>
          </div>
          <el-tree ref="treeRef" :data="currentTreeData" :props="treeProps" @node-click="handleNodeClick" :key="treeKey"
            class="group-tree" :expand-on-click-node="false" highlight-current node-key="id"
            :default-expand-all="defaultExpandAll">
            <template #default="{ data }">
              <div class="tree-node">
                <div class="case-name">{{ data.name }}</div>
                <div class="case-count">{{ data.count }} cases</div>
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 右侧：用例列表 -->
      <div class="dialog-right">
        <GeneratedCasesPanel :cases="displayedCases" :generating="false" :saving="false" :save-progress="null"
          :show-empty-message="showEmptyMessage" v-loading="loading" :element-loading-text="loadingText" />

        <!-- 分页组件 -->
        <Pagination v-if="pagedResult" :page-number="pagedResult.pageNumber" :page-size="pagedResult.pageSize"
          :total-count="pagedResult.totalCount" @page-change="handlePageChange" />
      </div>
    </div>

    <!-- 底部工具栏 -->
    <div class="toolbar bottom-toolbar">
      <el-button type="primary" @click="handleSave" :disabled="!canSave">Save</el-button>
    </div>

    <!-- 生成用例对话框 -->
    <GenerateCasesDialog v-model:visible="showGenerateDialog" :coverage="selectedCoverage"
      :selected-sequences="selectedSequences" :baseline-average-time="baselineAverageTime"
      @generated="onCasesGenerated" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { CoverageType, appApi } from '@/api/appApi';
import { CaseResult, GroupTreeNode, PagedResult, PagedQuery } from '@/api/interoperationApi';
import GenerateCasesDialog from '@/components/TestCases/GenerateCasesDialog.vue';
import GeneratedCasesPanel from '@/components/TestCases/GeneratedCasesPanel.vue';
import Pagination from '@/components/common/Pagination.vue';

// 状态变量
const loading = ref(true);
const loadingText = ref('');
const selectedCoverage = ref(CoverageType.Normal);
const selectedSequences = ref<string[]>([]);
const baselineAverageTime = ref<number>(0);
const showGenerateDialog = ref(false);

// 数据状态
const displayedCases = ref<CaseResult[]>([]);
const groupTree = ref<GroupTreeNode | null>(null);
const generationTime = ref<string>('');
const isGeneratedData = ref(false); // 标识当前显示的是否为生成的数据

// 分页相关状态
const currentQuery = ref<PagedQuery>({
  pageNumber: 1,
  pageSize: 1000,
  groupPath: ''
});
const pagedResult = ref<PagedResult<CaseResult> | null>(null);
const totalCaseCount = ref(0);

// 树形组件引用和状态
const treeRef = ref();
const defaultExpandAll = ref(false);
const currentTreeData = ref<GroupTreeNode[]>([]);
const treeKey = ref(new Date())

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'name'
};

// 计算属性
const showEmptyMessage = computed(() => {
  return displayedCases.value.length === 0;
});

const canSave = computed(() => {
  return isGeneratedData.value && totalCaseCount.value > 0;
});

const hasTreeData = computed(() => {
  return currentTreeData.value.length > 0 && !loading.value;
});

// 获取测试用例数据（使用新的分页机制）
const fetchTestCases = async () => {
  loadingText.value = "Loading test cases...";
  loading.value = true;

  try {
    let response;
    if (isGeneratedData.value) {
      // 获取生成的用例
      response = await appApi.getGeneratedCasesWithGroup(currentQuery.value);
    } else {
      // 获取已保存的用例  
      response = await appApi.getCasesWithGroup(currentQuery.value);
    }

    // 更新数据
    displayedCases.value = response.data.pagedCases.items;
    pagedResult.value = response.data.pagedCases;
    totalCaseCount.value = response.data.allCasesCount;

    // 如果生成时间戳变了，更新分组树
    if (generationTime.value != response.data.generationTime) {
      // 默认点击根节点
      groupTree.value = response.data.groupTree;
      currentTreeData.value = groupTree.value ? [groupTree.value] : [];
      handleNodeClick(groupTree.value);
    }
    generationTime.value = response.data.generationTime;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('获取测试用例失败:', error);
    ElMessage.error('Failed to fetch test cases');
  } finally {
    loading.value = false;
  }
};

// 分页变化处理
const handlePageChange = (pageNumber: number, pageSize: number) => {
  currentQuery.value.pageNumber = pageNumber;
  currentQuery.value.pageSize = pageSize;
  fetchTestCases();
};

const expandAll = () => {
  defaultExpandAll.value = true;
  treeKey.value = new Date();
};

const collapseAll = () => {
  defaultExpandAll.value = false;
  treeKey.value = new Date();
};

// 节点点击时筛选用例
const handleNodeClick = (node: any) => {
  const path = node.id;
  const isRootNode = !path.includes('/');

  // 更新查询条件
  currentQuery.value.groupPath = isRootNode ? '' : path;
  currentQuery.value.pageNumber = 1; // 重置到第一页

  // 重新获取数据
  fetchTestCases();
};

// 打开生成用例对话框
const openGenerateDialog = () => {
  showGenerateDialog.value = true;
};

// 用例生成成功的回调
const onCasesGenerated = (newGroupTree: GroupTreeNode, newGenerationTime: string) => {
  // 切换到生成数据模式
  isGeneratedData.value = true;
  generationTime.value = newGenerationTime;

  // 重置查询状态
  currentQuery.value = {
    pageNumber: 1,
    pageSize: 1000,
    groupPath: ''
  };

  // 更新分组树和总数
  groupTree.value = newGroupTree;
  currentTreeData.value = newGroupTree ? [newGroupTree] : [];
  totalCaseCount.value = newGroupTree ? newGroupTree.count : 0;

  // 重新获取数据
  fetchTestCases();
};

// 保存测试用例
const handleSave = async () => {
  if (!generationTime.value) {
    ElMessage.error('No generation time available. Please generate test cases first.');
    return;
  }

  try {
    loadingText.value = "Saving test cases...";
    loading.value = true;

    // 调用API保存用例
    await appApi.saveCases(generationTime.value);

    // 保存成功后切换到已保存数据模式
    isGeneratedData.value = false;

    // 重置查询状态并重新加载数据
    currentQuery.value = {
      pageNumber: 1,
      pageSize: 1000,
      groupPath: ''
    };

    ElMessage.success(`Test cases saved successfully`);

    // 重新获取已保存的数据
    await fetchTestCases();

  } catch (error) {
    console.error('保存测试用例失败:', error);
    ElMessage.error('Failed to save test cases');
  } finally {
    loading.value = false;
  }
};

// 组件挂载时获取测试用例
onMounted(() => {
  // 默认加载已保存的用例
  isGeneratedData.value = false;
  fetchTestCases();
});
</script>

<style scoped lang="scss">
.test-cases-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 15px 20px;
}

/* 顶部工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;

  .toolbar-left {
    display: flex;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
  }
}

/* 底部工具栏样式 */
.bottom-toolbar {
  margin-top: 15px;
  margin-bottom: 0;
  justify-content: flex-start;
}

/* 主要内容区域样式 */
.content-area {
  display: flex;
  flex: 1;
  min-height: 0;
  gap: 15px;
}

/* 左侧分组树样式 */
.dialog-left {
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fff;
  flex: 0.5;
  min-height: 0;
  min-width: 0;
}

.group-info {
  padding: 10px 15px;
  border-bottom: 1px solid #e4e7ed;
  font-size: 12px;
  color: #909399;
}

.group-border {
  flex: 1;
  display: flex;
  flex-direction: column;
  flex-basis: 0;
  min-height: 0;
}

.tree-header {
  padding: 10px 15px;
  border-bottom: 1px solid #e4e7ed;
  background: #f8f9fa;

  h4 {
    margin: 0;
    font-size: 14px;
    color: #303133;
  }
}

.group-tree {
  flex: 1;
  overflow-y: auto;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 6px;

  .case-name {
    flex: 1;
    font-size: 13px;
  }

  .case-count {
    font-size: 12px;
    color: #909399;
    margin-left: 10px;
  }
}

/* 右侧用例列表样式 */
.dialog-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fff;
}

.button-text {
  margin-left: 8px;
}
</style>
