import { mockSuccess, mockError } from '../mockApi';
import {
  HardwareConfig,
  DeviceChannel,
  TestPlanConfig
} from '@/api/hardwareApi';
import { AxiosResponse } from 'axios';

// 模拟设备数据
const mockDeviceChannels: DeviceChannel[] = [
  {
    name: "VN1630 123456",
    communicationType: "Can",
    isConnected: true
  },
  {
    name: "VN1640 789012",
    communicationType: "CanFd",
    isConnected: true
  },
  {
    name: "TC1001 ABCDEF",
    communicationType: "Can",
    isConnected: true
  },
  {
    name: "TC2001 XYZ789",
    communicationType: "CanFd",
    isConnected: false
  }
];

// 默认测试计划配置
let currentTestPlanConfig: TestPlanConfig = {
  communicationType: "Can",
  canConfig: {
    deviceChannelName: "VN1630 123456",
    dataBitrate: 500000
  },
  canFdConfig: {
    deviceChannelName: "VN1640 789012",
    arbitrationBitrate: 500000,
    dataBitrate: 2000000
  }
};

// 设置定时器，随机改变设备连接状态
setInterval(() => {
  mockDeviceChannels.forEach(device => {
    // 20%的概率改变连接状态
    if (Math.random() < 0.8) {
      device.isConnected = !device.isConnected;
    }
  });
}, 3000); // 每3秒检查一次

export const mockHardwareApi = {
  // 获取硬件配置
  getHardwareConfig: (): Promise<AxiosResponse<HardwareConfig>> => {
    const config: HardwareConfig = {
      deviceChannels: mockDeviceChannels,
      testPlanConfig: currentTestPlanConfig
    };

    return mockSuccess(config);
  },

  // 更新硬件配置
  updateHardwareConfig: (config: TestPlanConfig): Promise<AxiosResponse<HardwareConfig>> => {
    if (!config) {
      return mockError(400, "无效的配置数据");
    }

    // 更新当前配置
    currentTestPlanConfig = {
      ...currentTestPlanConfig,
      ...config
    };

    // 基于通道类型确保相应配置存在
    if (config.communicationType === "Can" && config.canConfig) {
      currentTestPlanConfig.canConfig = { ...config.canConfig };
    } else if (config.communicationType === "CanFd" && config.canFdConfig) {
      currentTestPlanConfig.canFdConfig = { ...config.canFdConfig };
    }

    // 返回更新后的完整配置
    const updatedConfig: HardwareConfig = {
      deviceChannels: mockDeviceChannels,
      testPlanConfig: currentTestPlanConfig
    };

    return mockSuccess(updatedConfig);
  }
};
