<template>
  <div class="result-panel">
    <div class="panel-header">
      <h3>Interoperation Results</h3>
    </div>

    <!-- 新增: 显示重复序列名称警告 -->
    <div v-if="duplicateSequences.length > 0" class="duplicate-warning">
      <el-alert title="Warning: Duplicate sequence names detected" type="warning" :closable="false" show-icon>
        <template #default>
          Found duplicate names: {{ duplicateSequences.join(', ') }}.<br />
          This may cause selection issues.
        </template>
      </el-alert>
    </div>

    <div v-if="loading" class="loading-indicator">
      <el-icon class="is-loading">
        <Loading />
      </el-icon> Loading...
    </div>

    <div v-else-if="results.length === 0" class="empty-message">
      No interoperation results available
    </div>

    <div v-else class="results-list">
      <!-- 全选复选框 -->
      <div class="select-all-row">
        <el-checkbox v-model="isAllSelected" :indeterminate="isIndeterminate" @change="handleSelectAllChange"
          size="small">
          Select All ({{ selectedCount }}/{{ results.length }})
        </el-checkbox>
        <div v-if="averageTime && averageTime > 0" class="average-time">
          Average elapsed: {{ formatDuration(averageTime) }}
        </div>
      </div>

      <div class="result-rows">
        <!-- 优化后的单行项布局 -->
        <div v-for="(item, index) in results" :key="item.id || `item-${index}`" class="list-item"
          :class="{ 'striped': index % 2 === 1, 'duplicate': isDuplicate(item.sequenceName) }"
          @click.stop="handleItemClick(index)">
          <!-- 使用直接的勾选元素 -->
          <div class="item-checkbox">
            <el-checkbox v-model="selectedItems[index]" size="small" @click.stop
              @change="(val: boolean) => handleItemSelect(val, index, item)" />
          </div>

          <div class="item-name">
            {{ item.sequenceName }}
            <el-tag v-if="isDuplicate(item.sequenceName)" size="small" type="warning" effect="dark"
              class="duplicate-tag">
              Duplicate
            </el-tag>
          </div>

          <div class="item-status">
            <CaseStateTag :state="item.state" />
          </div>

          <div class="item-duration">
            <TimeDisplay :begin="item.begin" :end="item.end" :showDuration="true" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, computed, watch, onMounted } from 'vue';
import { Loading } from '@element-plus/icons-vue';
import { CaseResult } from '@/api/interoperationApi';
import { ExecutionState } from '@/api/appApi';
import { ElAlert } from 'element-plus';
import CaseStateTag from '@/components/common/CaseStateTag.vue';
import TimeDisplay from '@/components/common/TimeDisplay.vue';
import { formatDuration } from '@/utils/timeUtils';

const props = defineProps<{
  results: CaseResult[];
  loading: boolean;
  selectedSequences?: string[];
  averageTime?: number; // 平均时间(毫秒)
}>();

const emit = defineEmits<{
  (e: 'update:selectedSequences', value: string[]): void;
}>();

// 选中的项目数组
const selectedItems = ref<boolean[]>([]);

// 初始化选中项，在 onMounted 中处理
onMounted(() => {
  initializeSelectedItems();
});

// 监听结果变化，更新选中状态
watch(() => props.results, () => {
  initializeSelectedItems();
}, { immediate: false });

// 监听外部传入的已选中序列
watch(() => props.selectedSequences, (newSelected) => {
  if (newSelected && props.results.length > 0) {
    selectedItems.value = props.results.map(item =>
      newSelected.includes(item.sequenceName)
    );
  }
}, { deep: true });

// 初始化选中状态函数 - 默认勾选成功的结果
function initializeSelectedItems() {
  if (props.results.length > 0) {
    // 默认勾选成功的结果
    selectedItems.value = props.results.map(result => result.state === ExecutionState.Success);
    updateSelectedSequences();
  }
}

// 计算全选状态
const isAllSelected = computed(() => {
  return selectedItems.value.length > 0 && selectedItems.value.every(selected => selected);
});

// 计算部分选中状态
const isIndeterminate = computed(() => {
  return selectedItems.value.some(selected => selected) && !isAllSelected.value;
});

// 计算选中的数量
const selectedCount = computed(() => {
  return selectedItems.value.filter(selected => selected).length;
});

// 处理全选/取消全选
const handleSelectAllChange = (val: boolean) => {
  selectedItems.value = new Array(props.results.length).fill(val);
  updateSelectedSequences();
};

// 新增: 点击项目时切换对应的复选框状态
const handleItemClick = (index: number) => {
  selectedItems.value[index] = !selectedItems.value[index];
  updateSelectedSequences();
};

// 处理单个项目选中/取消选中
const handleItemSelect = (val: boolean, index: number, item: CaseResult) => {
  selectedItems.value[index] = val;
  updateSelectedSequences();
};

// 更新选中的序列列表并触发事件 - 修正为使用索引跟踪
const updateSelectedSequences = () => {
  // 根据索引获取选中的结果项，然后提取其序列名称
  const selected = props.results
    .filter((_, index) => selectedItems.value[index])
    .map(item => item.sequenceName);

  // 使用Set去重，确保不会因为重复的sequenceName导致问题
  const uniqueSelected = [...new Set(selected)];

  emit('update:selectedSequences', uniqueSelected);
};

// 检测重复的序列名称
const duplicateSequences = computed(() => {
  if (!props.results || props.results.length === 0) return [];

  const nameCounts = new Map<string, number>();
  const duplicates = new Set<string>();

  // 统计每个序列名称出现的次数
  props.results.forEach(result => {
    const count = nameCounts.get(result.sequenceName) || 0;
    nameCounts.set(result.sequenceName, count + 1);

    // 如果出现次数超过1，则是重复的
    if (count > 0) {
      duplicates.add(result.sequenceName);
    }
  });

  return Array.from(duplicates);
});

// 检查特定的序列名称是否重复
const isDuplicate = (name: string) => {
  return duplicateSequences.value.includes(name);
};
</script>

<style scoped lang="scss">
.result-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.panel-header {
  padding: 8px 12px;
  border-bottom: 1px solid #ebeef5;

  h3 {
    margin: 0;
    font-size: 15px;
    font-weight: 500;
    color: #303133;
  }

  .panel-subtitle {
    font-size: 12px;
    color: #909399;
    margin-top: 2px;
  }
}

.select-all-row {
  padding: 8px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: var(--el-fill-color-light);
  display: flex;
  justify-content: space-between;
  align-items: center;

  .average-time {
    font-size: 12px;
    color: #909399;
    font-weight: 500;
  }
}

.results-list {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.result-rows {
  display: flex;
  flex-direction: column;
  overflow: auto;
  flex: 1;
  flex-basis: 0;
}

.list-item {
  font-size: 13px;
  display: flex;
  align-items: center;
  padding: 8px 20px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
  transition: background-color 0.2s;

  &:last-child {
    border-bottom: none;
  }

  &.striped {
    background-color: #fafafa;
  }

  &:hover {
    background-color: #f5f7fa;
  }

  /* 单行布局样式 */
  .item-checkbox {
    flex: 0 0 auto;
    margin-right: 10px;
  }

  .item-name {
    flex: 1;
    font-weight: 500;
    color: #303133;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .item-status {
    flex: 0 0 80px;
    text-align: right;
  }

  .item-duration {
    text-align: right;
    color: #909399;
    font-size: 12px;
    min-width: 60px;
  }
}

.loading-indicator,
.empty-message {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  color: #909399;
  font-size: 13px;

  .el-icon {
    margin-right: 6px;
  }
}

.duplicate-warning {
  margin: 0 12px;
  padding-top: 12px;
}

.duplicate-tag {
  margin-left: 8px;
  font-size: 10px;
  line-height: 1;
  padding: 2px 4px;
  border-radius: 2px;
}

.list-item {
  // ...existing code...

  &.duplicate {
    background-color: rgba(255, 229, 100, 0.1);

    &:hover {
      background-color: rgba(255, 229, 100, 0.2);
    }
  }
}
</style>
