import { reactive, readonly } from 'vue';
import { ElMessage } from 'element-plus';
import { Router } from 'vue-router';
import { testPlanApi, type TestPlan } from '@/api/testPlanApi';
import { testPlanHistoryApi, type TestPlanHistory } from '@/api/testPlanHistoryApi';

// 定义状态接口
interface TestPlanState {
  currentPlan: TestPlan | null;
  recentPlans: TestPlanHistory[];
  isLoading: boolean;
}

// 创建内部状态
const state = reactive<TestPlanState>({
  currentPlan: null,
  recentPlans: [],
  isLoading: false
});

// 测试计划服务类
class TestPlanServiceClass {
  private router: Router | null = null;

  // 初始化路由
  setRouter(router: Router) {
    this.router = router;
  }

  // 获取只读状态
  getState() {
    return readonly(state);
  }

  // 打开文件浏览器选择测试计划
  async openFromExplorer() {
    state.isLoading = true;
    try {
      const response = await testPlanApi.openInExplorer();
      if (response.data) {
        state.currentPlan = response.data;
        this.navigateToTestPlan();
        await this.loadRecentPlans();
        return response.data;
      }
    } finally {
      state.isLoading = false;
    }
    return null;
  }

  // 从历史记录中打开测试计划
  async openFromPath(path: string) {
    state.isLoading = true;
    try {
      const response = await testPlanApi.open(path);
      if (response.data) {
        state.currentPlan = response.data;
        this.navigateToTestPlan();
        await this.loadRecentPlans();
        return response.data;
      }
    } finally {
      state.isLoading = false;
    }
    return null;
  }

  // 创建新的测试计划
  async createTestPlan(testPlanData: { name: string; description: string; folder: string }) {
    state.isLoading = true;
    try {
      const response = await testPlanApi.create(testPlanData);
      if (response.data) {
        state.currentPlan = response.data;
        ElMessage.success("Test plan created successfully");
        this.navigateToTestPlan();
        await this.loadRecentPlans();
        return response.data;
      }
    } catch (error: any) {
      ElMessage.error("Failed to create test plan");
      console.error(error);
    } finally {
      state.isLoading = false;
    }
    return null;
  }

  // 关闭测试计划
  async closeTestPlan() {
    try {
      await testPlanApi.close();
      state.currentPlan = null;
      if (this.router) {
        this.router.push('/');
      }
    } catch (error: any) {
      ElMessage.error("Failed to close test plan");
      console.error(error);
    }
  }

  // 加载最近的测试计划
  async loadRecentPlans() {
    try {
      const response = await testPlanHistoryApi.get();
      state.recentPlans = response.data;
      return response.data;
    } catch (error) {
      console.error('Failed to load recent plans:', error);
      return [];
    }
  }

  // 清空历史记录
  async clearHistory() {
    try {
      await testPlanHistoryApi.clear();
      state.recentPlans = [];
      ElMessage.success("History cleared");
      return true;
    } catch (error) {
      ElMessage.error("Failed to clear history");
      console.error(error);
      return false;
    }
  }

  // 更新测试计划基本信息
  async updateBasicInfo(description: string) {
    state.isLoading = true;
    try {
      const response = await testPlanApi.updateBasicInfo(description);
      if (response.data) {
        state.currentPlan = response.data;
        ElMessage.success("Description updated successfully");
        return response.data;
      }
    } catch (error) {
      ElMessage.error("Failed to update description");
      console.error('Error updating description:', error);
    } finally {
      state.isLoading = false;
    }
    return null;
  }

  // 获取当前测试计划
  async getCurrentPlan() {
    if (state.currentPlan) {
      return state.currentPlan;
    }

    state.isLoading = true;
    try {
      const response = await testPlanApi.getCurrentPlan();
      state.currentPlan = response.data;
      return response.data;
    } catch (error) {
      console.error('Error loading current plan:', error);
      return null;
    } finally {
      state.isLoading = false;
    }
  }

  // 导航到测试计划页面
  private navigateToTestPlan() {
    if (this.router) {
      this.router.push({ name: 'test-plan.basic-setting' });
    }
  }
}

// 创建服务实例
export const testPlanService = new TestPlanServiceClass();
