<template>
  <div class="app-container">
    <TopMenuBar />
    <div class="main-content">
      <main class="content">
        <router-view />
      </main>
    </div>
    <StatusBar />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import TopMenuBar from '@/components/layout/TopMenuBar.vue'
import StatusBar from '@/components/layout/StatusBar.vue'
import { LicenseReminderStorage } from '@/utils/licenseReminderStorage'

// 初始化 License 提醒存储管理器
onMounted(() => {
  LicenseReminderStorage.initialize()
})
</script>

<style lang="scss">
#app {
  color: var(--el-text-color-primary);
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  position: relative;
  background-color: #f5f7fa;
  min-height: 0; /* 确保flex子项可以正确收缩 */
}

.content {
  flex: 1;
  overflow: hidden;
}

body {
  margin: 0;
  padding: 0;
  color: var(--el-text-color-primary);
  background-color: var(--el-bg-color);
}
</style>
