{"name": "fuzz-web", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@codemirror/commands": "^6.3.3", "@codemirror/lang-xml": "^6.0.2", "@codemirror/language": "^6.10.1", "@codemirror/state": "^6.3.3", "@codemirror/view": "^6.24.1", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "axios": "^1.4.0", "core-js": "^3.8.3", "element-plus": "^2.10.5", "js-booster": "^1.1.3", "vue": "^3.2.13", "vue-router": "^4.0.3", "vuex": "^4.0.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-typescript": "^9.1.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sass": "^1.32.7", "sass-loader": "^12.0.0", "typescript": "~4.7.0"}}