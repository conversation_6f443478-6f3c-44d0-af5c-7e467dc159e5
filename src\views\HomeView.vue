<template>
  <div class="home-view">
    <el-row class="layout-container" :gutter="20">
      <!-- 左侧面板 -->
      <el-col :span="12" class="left-panel">
        <el-card class="control-card">
          <template #header>
            <div class="panel-header">
              <div class="title-section">
                <h2>Test Plans</h2>
              </div>
              <div class="button-group">
                <el-button type="primary" class="action-btn" @click="handleOpenTestPlan">
                  <el-icon><Folder /></el-icon>
                  <span>Open</span>
                </el-button>
                <el-button type="success" class="action-btn" @click="showCreateDialog">
                  <el-icon><Plus /></el-icon>
                  <span>Create</span>
                </el-button>
              </div>
            </div>
          </template>

          <!-- 历史记录 -->
          <div class="history-container">
            <test-plan-history ref="historyComponent" @open-plan="handleOpenFromHistory" />
          </div>
        </el-card>
      </el-col>

      <!-- 右侧面板 -->
      <el-col :span="12" class="right-panel">
        <el-card class="guide-card">
          <template #header>
            <div class="panel-header">
              <div class="title-section">
                <h2>Guide</h2>
              </div>
            </div>
          </template>
          <test-plan-guide />
        </el-card>
      </el-col>
    </el-row>

    <create-test-plan ref="createDialog" @created="handleTestPlanCreated" />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from "vue";
import { Folder, Plus } from '@element-plus/icons-vue';
import { useRouter } from "vue-router";
import CreateTestPlan from '@/components/TestPlan/CreateTestPlan.vue';
import TestPlanHistory from '@/components/TestPlan/TestPlanHistory.vue';
import TestPlanGuide from '@/components/Guide/TestPlanGuide.vue';
import { testPlanService } from '@/services/testPlanService';
import { useLicenseCheck } from '@/utils/useLicenseCheck';
import type { CreateTestPlanInstance, TestPlanHistoryInstance } from '@/components/TestPlan/types';

export default defineComponent({
  name: "HomeView",
  components: {
    CreateTestPlan,
    TestPlanHistory,
    TestPlanGuide,
    Folder,
    Plus
  },
  setup() {
    const router = useRouter();
    const createDialog = ref<CreateTestPlanInstance | null>(null);
    const historyComponent = ref<TestPlanHistoryInstance | null>(null);
    const { checkLicenseWithPrompt } = useLicenseCheck();

    // 设置路由
    testPlanService.setRouter(router);

    // 处理打开测试计划
    const handleOpenTestPlan = async () => {
      const result = await checkLicenseWithPrompt();
      if (result.hasValidLicense) {
        await testPlanService.openFromExplorer();
        historyComponent.value?.refresh();
      }
    };

    // 从历史记录打开测试计划
    const handleOpenFromHistory = async (path: string) => {
      const result = await checkLicenseWithPrompt();
      if (result.hasValidLicense) {
        await testPlanService.openFromPath(path);
        historyComponent.value?.refresh();
      }
    };

    // 显示创建对话框
    const showCreateDialog = async () => {
      const result = await checkLicenseWithPrompt();
      if (result.hasValidLicense) {
        createDialog.value?.show();
      }
    };

    // 处理测试计划创建完成
    const handleTestPlanCreated = () => {
      historyComponent.value?.refresh();
    };

    return {
      createDialog,
      historyComponent,
      handleOpenTestPlan,
      showCreateDialog,
      handleTestPlanCreated,
      handleOpenFromHistory
    };
  },
});
</script>

<style scoped>
.home-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  box-sizing: border-box;
}

.layout-container {
  flex: 1;
  min-height: 0;
}

.left-panel, .right-panel {
  height: 100%;
}

.control-card, .guide-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 32px;
}

.title-section {
  display: flex;
  align-items: center;
}

.panel-header h2 {
  margin: 0;
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 20px;
}

.button-group {
  display: flex;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  justify-content: center;
}

.history-container {
  flex: 1;
}

/* 响应式设计 */
@media screen and (max-width: 1200px) {
  .panel-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .button-group {
    width: 100%;
    justify-content: space-between;
  }
  
  .action-btn {
    flex: 1;
    justify-content: center;
  }
}

</style>
