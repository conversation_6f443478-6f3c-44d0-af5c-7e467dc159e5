import { mockSuccess } from '../mockApi';
import { CaseConfigDto } from '@/api/caseApi';
import { CaseResult } from '@/api/interoperationApi';
import { ExecutionState } from '@/api/appApi';

const defaultConfig: CaseConfigDto = {
  whiteListFrames: [], // 修改为空数组
  selectedNodeName: '', // 新增：默认没有选中节点
  enableNmWakeup: true,
  nmWakeupId: 0x53F,
  nmWakeupIsExt: false,
  nmWakeupDlc: 8,
  nmWakeupData: [0x3F, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF],
  nmWakeupCommunicationType: 'Can',
  nmWakeupCycleMs: 100,
  nmWakeupDelayMs: 2000,
  requestId: 0x731,
  requestIsExt: false,
  responseId: 0x631,
  timeoutMs: 500,
  isDutMtuLessThan4096: false, // 新增：默认不限制MTU大小
  enableDiagRetryRequest: false, // 默认不启用备用诊断请求
  diagRetryRequestPayload: [0x10, 0x01], // 默认备用诊断请求数据

  // 安全配置信息
  securityInfo: {
    hasDll: false,
    dllFileName: undefined,
    dllSize: 0
  }
};

// 模拟一个CaseResult对象，用于返回单个用例详情
const mockSingleCaseResult: CaseResult = {
  id: 1,
  testResultId: '00000000-0000-0000-0000-000000000000',
  sequenceId: '00000000-0000-0000-0000-000000000000',
  sequenceName: 'Test Sequence',
  name: 'mock case result',
  parameter: 'Parameter Value',
  state: ExecutionState.Success,
  begin: new Date().toISOString(),
  end: new Date().toISOString(),
  detail: 'Case result details',
  groupPath: '',
  testerGroup: ''
};



// 模拟生成用例数量
let mockGeneratedCasesCount = 0;

export const mockCaseApi = {
  getCaseConfig() {
    return mockSuccess(defaultConfig);
  },

  getCaseResult(testResultId: string, caseResultId: number) {
    // 简单模拟，实际应用中可以根据ID返回不同的结果
    return mockSuccess({
      ...mockSingleCaseResult,
      id: caseResultId,
      testResultId: testResultId
    });
  },

  updateCaseConfig(config: CaseConfigDto) {
    // 处理安全DLL操作
    if (config.removeSecurityDll) {
      // 移除DLL
      config.securityInfo = {
        hasDll: false,
        dllFileName: undefined,
        dllSize: 0
      };
    } else if (config.securityDllPath) {
      // 选择新的DLL - 模拟DLL文件已加载
      const dllName = config.securityDllPath.split('\\').pop() || 'SecurityAccess.dll';
      const mockDllSize = 15360; // 15KB

      config.securityInfo = {
        hasDll: true,
        dllFileName: dllName,
        dllSize: mockDllSize
      };
    }

    // 清除操作标记，模拟后端API的行为
    delete config.securityDllPath;
    delete config.removeSecurityDll;

    return mockSuccess(config);
  },

  importDbc() {
    return mockSuccess({
      whiteListFrames: [ // 更新帧列表，添加发送者和接收者信息
        { id: 0x123, name: 'Engine_Status', dlc: 8, isExt: false, transmitter: 'ECM', receivers: ['TCM', 'BCM'] },
        { id: 0x456, name: 'Transmission_Data', dlc: 8, isExt: true, transmitter: 'TCM', receivers: ['ECM', 'ICM'] },
        { id: 0x789, name: 'Brake_Control', dlc: 8, isExt: false, transmitter: 'BCM', receivers: ['ECM', 'TCM', 'ICM'] }
      ],
      nodeNames: ['ECM', 'TCM', 'BCM', 'ICM'] // 添加节点名称列表
    });
  },

  // 新增：选择安全DLL文件
  selectSecurityDll() {
    return mockSuccess({
      path: "C:\\fakepath\\SecurityAccess.dll"
    });
  },



  // 保存测试用例 - 更新为接受生成时间戳参数
  saveCases(generationTime: string) {
    console.log(`模拟保存用例，生成时间戳: ${generationTime}`);
    return mockSuccess({ message: 'Cases saved successfully' });
  },

  // 获取当前生成的用例数量
  getGeneratingProgress() {
    return mockSuccess(`${++mockGeneratedCasesCount} cases generated...`);
  }
};
