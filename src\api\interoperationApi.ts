import axios, { AxiosResponse } from 'axios'
import { USE_MOCK, mockApi } from '@/mock/mockApi'
import { TesterSnapshot, ExecutionState } from './appApi';

// 基础响应类型
export interface BaseResponse {
  success: boolean;
  message: string;
}

// 用例结果类型
export interface CaseResult {
  id: number;
  testResultId: string; // Guid in C#
  sequenceId: string; // Guid in C#
  sequenceName: string;
  name: string;
  groupPath: string;
  parameter: string;
  state: ExecutionState;
  begin: string | null;
  end: string | null;
  detail: string;
  testerGroup: string;
}

// 类型定义
export interface GroupTreeNode {
  name?: string;
  children: GroupTreeNode[];
  id?: string;
  // parent?: GroupTreeNode;
  count: number;
}

// 分页查询请求
export interface PagedQuery {
  pageNumber?: number;
  pageSize?: number;
  groupPath?: string;
}

// 分页结果
export interface PagedResult<T> {
  items: T[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
}

// 包含分页的分组响应
export interface PagedGroupResponse {
  pagedCases: PagedResult<CaseResult>;
  groupTree: GroupTreeNode;
  generationTime: string;
  allCasesCount: number;
}

export interface MyGroupResponse {
  cases: CaseResult[];
  groupTree: GroupTreeNode
}

// 生成用例响应，包含生成时间戳
export interface GenerateCasesResponse {
  groupTree: GroupTreeNode;
  generationTime: string;
}


// 互操作测试状态类型
export interface InteroperationStatus {
  isRunning: boolean;
  progress: number;
  currentOperation: string;
  beginTime: string | null;
  endTime: string | null;
  summary: {
    passed: number;
    failed: number;
    skipped: number;
  };
  caseResults: CaseResult[] | null;
}

export function isTesterCompleted(tester: TesterSnapshot): boolean {
  return tester.processState === ExecutionState.Success ||
    tester.processState === ExecutionState.Failure;
}

const BASE_URL = '/api/interoperation'

export const interoperationApi = {
  // 启动互操作测试
  startTest: (): Promise<AxiosResponse<BaseResponse>> => {
    if (USE_MOCK) {
      return mockApi.interoperation.startTest();
    }
    return axios.post(`${BASE_URL}/start`);
  },

  // 停止互操作测试
  stopTest: (): Promise<AxiosResponse<BaseResponse>> => {
    if (USE_MOCK) {
      return mockApi.interoperation.stopTest();
    }
    return axios.post(`${BASE_URL}/stop`);
  },

  // 获取测试状态
  getStatus: (): Promise<AxiosResponse<TesterSnapshot>> => {
    if (USE_MOCK) {
      return mockApi.interoperation.getStatus();
    }
    return axios.get(`${BASE_URL}/status`);
  }
}

export default interoperationApi;
