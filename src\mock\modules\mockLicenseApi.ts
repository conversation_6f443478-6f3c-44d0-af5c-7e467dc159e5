import { mockSuccess } from '../mockApi'
import { LicInfo, LicFileInfo } from '../../api/licenseApi'

// 模拟的许可证文件信息
// 为了测试提醒功能，设置一个即将到期的 license（剩余 5 天）
const mockLicFileInfo: LicFileInfo = {
  id: 'lic-12345-67890',
  fileName: 'test-license.lic',
  creationTime: '2025-01-15T08:30:00Z',
  isExpired: false,
  expirationTime: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(), // 5天后到期
  remainingSeconds: 5 * 24 * 60 * 60, // 5 days in seconds (用于测试提醒)
  audience: 'TestAudience',
  machineCode: 'HW-ABC123-DEF456',
  issuer: 'Alsi License System',
  issueTime: '2025-01-15T08:30:00Z'
}

// 模拟的许可证信息状态
let mockLicInfo: LicInfo = {
  machineCode: 'HW-ABC123-DEF456',
  hasLicFile: true,
  licFileInfo: mockLicFileInfo,
  matchMachineCode: true // 可以设置为 false 来测试机器码不匹配的情况
}

export const mockLicenseApi = {
  // 获取许可证信息
  getLicenseInfo: () => {
    console.log('Mock: Getting license info')
    return mockSuccess(mockLicInfo)
  },

  // 通过服务端文件选择器选择并上传许可证文件
  selectAndUploadLicense: () => {
    console.log('Mock: Selecting and uploading license via server file dialog')
    
    // 模拟文件选择和上传成功，更新许可证信息
    const fileName = 'server-selected-license.lic'
    
    mockLicInfo = {
      ...mockLicInfo,
      hasLicFile: true,
      matchMachineCode: true,
      licFileInfo: {
        ...mockLicFileInfo,
        fileName: fileName,
        creationTime: new Date().toISOString(),
        issueTime: new Date().toISOString()
      }
    }
    
    return mockSuccess(mockLicInfo)
  },

  // 删除许可证
  deleteLicense: (licenseId: string) => {
    console.log('Mock: Deleting license:', licenseId)
    
    // 模拟删除成功，清空许可证信息
    mockLicInfo = {
      ...mockLicInfo,
      hasLicFile: false,
      matchMachineCode: false,
      licFileInfo: {
        id: '',
        fileName: '',
        creationTime: '',
        isExpired: false,
        expirationTime: '',
        remainingSeconds: 0,
        audience: '',
        machineCode: mockLicInfo.machineCode,
        issuer: '',
        issueTime: ''
      }
    }
    
    return mockSuccess({ success: true, message: 'License deleted successfully' })
  }
}
